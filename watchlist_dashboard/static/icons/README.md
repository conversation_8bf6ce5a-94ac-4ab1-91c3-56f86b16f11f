# Icons and Favicon Setup

## Current Status
- ✅ `chart.svg` - Main application icon (blue circle with chart bars)
- ✅ `manifest.json` - PWA manifest for installable app
- ⚠️ `favicon.ico` - Needs to be generated

## How to Generate favicon.ico

To create a proper favicon.ico file from our SVG:

### Option 1: Online Converter
1. Go to https://favicon.io/favicon-converter/
2. Upload the `chart.svg` file
3. Download the generated `favicon.ico`
4. Place it in this `/static/icons/` directory

### Option 2: Using ImageMagick (if installed)
```bash
# Convert SVG to ICO (requires ImageMagick)
convert chart.svg -background transparent -resize 32x32 favicon.ico
```

### Option 3: Using Python PIL (if installed)
```python
from PIL import Image
import cairosvg

# Convert SVG to PNG first
cairosvg.svg2png(url='chart.svg', write_to='temp.png', output_width=32, output_height=32)

# Convert PNG to ICO
img = Image.open('temp.png')
img.save('favicon.ico', format='ICO', sizes=[(32, 32)])
```

## Icon Specifications

### Main Icon (chart.svg)
- Size: 32x32 viewBox
- Colors: Blue (#3b82f6) background, white elements
- Design: Chart bars with trend line and data points
- Format: SVG (scalable)

### Required Additional Icons (for PWA)
- `icon-192.png` - 192x192 PNG
- `icon-512.png` - 512x512 PNG  
- `apple-touch-icon.png` - 180x180 PNG

These can be generated from the SVG using the same conversion methods above.

## Current Favicon Fallback
The template currently includes a base64-encoded data URL version of the SVG as a fallback, which should work in most modern browsers that support SVG favicons.