/* 
Stock Watchlist Dashboard - Main Styles
Modern, responsive styling with theme support and beautiful animations
*/

:root {
    /* Extended color palette */
    --accent-gradient: linear-gradient(135deg, var(--accent-primary), var(--accent-secondary));
    --success-light: #d1fae5;
    --success-dark: #065f46;
    --warning-light: #fef3c7;
    --warning-dark: #92400e;
    --danger-light: #fee2e2;
    --danger-dark: #991b1b;
    --info-light: #e0f2fe;
    --info-dark: #0c4a6e;
    
    /* Typography */
    --font-weight-normal: 400;
    --font-weight-medium: 500;
    --font-weight-semibold: 600;
    --font-weight-bold: 700;
    
    /* Layout */
    --container-max-width: 1200px;
    --sidebar-width: 280px;
    --header-height: 70px;
}

/* Enhanced button styles */
.btn {
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-sm) var(--spacing-md);
    border: none;
    border-radius: var(--radius-md);
    font-weight: var(--font-weight-medium);
    text-decoration: none;
    cursor: pointer;
    transition: all var(--transition-fast);
    font-size: 0.875rem;
    line-height: 1.5;
    white-space: nowrap;
}

.btn:focus {
    outline: 2px solid var(--accent-primary);
    outline-offset: 2px;
}

.btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.btn-primary {
    background-color: var(--accent-primary);
    color: white;
}

.btn-primary:hover:not(:disabled) {
    background-color: var(--accent-secondary);
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.btn-secondary {
    background-color: var(--bg-primary);
    color: var(--text-primary);
    border: 1px solid var(--border-primary);
}

.btn-secondary:hover:not(:disabled) {
    background-color: var(--bg-tertiary);
    border-color: var(--accent-primary);
}

.btn-success {
    background-color: var(--success);
    color: white;
}

.btn-success:hover:not(:disabled) {
    background-color: var(--success-dark);
}

.btn-warning {
    background-color: var(--warning);
    color: white;
}

.btn-warning:hover:not(:disabled) {
    background-color: var(--warning-dark);
}

.btn-danger {
    background-color: var(--danger);
    color: white;
}

.btn-danger:hover:not(:disabled) {
    background-color: var(--danger-dark);
}

.btn-sm {
    padding: var(--spacing-xs) var(--spacing-sm);
    font-size: 0.75rem;
}

.btn-lg {
    padding: var(--spacing-md) var(--spacing-lg);
    font-size: 1rem;
}

/* Form styles */
.form-group {
    margin-bottom: var(--spacing-lg);
}

.form-label {
    display: block;
    margin-bottom: var(--spacing-xs);
    font-weight: var(--font-weight-medium);
    color: var(--text-primary);
}

.form-input {
    width: 100%;
    padding: var(--spacing-sm) var(--spacing-md);
    border: 1px solid var(--border-primary);
    border-radius: var(--radius-md);
    background-color: var(--bg-primary);
    color: var(--text-primary);
    font-size: 0.875rem;
    transition: all var(--transition-fast);
}

.form-input:focus {
    outline: none;
    border-color: var(--accent-primary);
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.form-input::placeholder {
    color: var(--text-muted);
}

.form-select {
    appearance: none;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
    background-position: right var(--spacing-sm) center;
    background-repeat: no-repeat;
    background-size: 1.5em 1.5em;
    padding-right: 2.5rem;
}

.form-textarea {
    resize: vertical;
    min-height: 80px;
}

.form-error {
    color: var(--danger);
    font-size: 0.75rem;
    margin-top: var(--spacing-xs);
}

/* Card styles */
.card {
    background-color: var(--bg-primary);
    border: 1px solid var(--border-primary);
    border-radius: var(--radius-lg);
    overflow: hidden;
    transition: all var(--transition-normal);
}

.card:hover {
    box-shadow: var(--shadow-md);
}

.card-header {
    padding: var(--spacing-lg);
    border-bottom: 1px solid var(--border-primary);
    background-color: var(--bg-secondary);
}

.card-body {
    padding: var(--spacing-lg);
}

.card-footer {
    padding: var(--spacing-lg);
    border-top: 1px solid var(--border-primary);
    background-color: var(--bg-secondary);
}

/* Modal styles */
.modal-overlay {
    position: fixed;
    inset: 0;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    opacity: 0;
    transition: opacity var(--transition-normal);
}

.modal-overlay.show {
    opacity: 1;
}

.modal {
    background-color: var(--bg-primary);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-xl);
    max-width: 500px;
    width: 90%;
    max-height: 90vh;
    overflow-y: auto;
    transform: scale(0.95);
    transition: transform var(--transition-normal);
}

.modal-overlay.show .modal {
    transform: scale(1);
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-lg);
    border-bottom: 1px solid var(--border-primary);
}

.modal-title {
    margin: 0;
    font-size: 1.25rem;
    font-weight: var(--font-weight-semibold);
    color: var(--text-primary);
}

.modal-close {
    background: none;
    border: none;
    font-size: 1.5rem;
    color: var(--text-muted);
    cursor: pointer;
    transition: color var(--transition-fast);
}

.modal-close:hover {
    color: var(--text-primary);
}

.modal-body {
    padding: var(--spacing-lg);
}

.modal-footer {
    display: flex;
    justify-content: flex-end;
    gap: var(--spacing-sm);
    padding: var(--spacing-lg);
    border-top: 1px solid var(--border-primary);
}

/* Chart container styles */
.chart-container {
    position: relative;
    width: 100%;
    height: 300px;
    margin: var(--spacing-lg) 0;
}

.chart-container.large {
    height: 400px;
}

.chart-container.small {
    height: 200px;
}

.chart-loading {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: var(--text-muted);
}

/* Metric visualization styles */
.metric-visual {
    position: relative;
    margin: var(--spacing-md) 0;
}

.metric-visual.simple {
    text-align: center;
    padding: var(--spacing-lg);
    background: var(--accent-gradient);
    border-radius: var(--radius-lg);
    color: white;
}

.metric-visual.simple .value {
    font-size: 2.5rem;
    font-weight: var(--font-weight-bold);
    margin-bottom: var(--spacing-sm);
}

.metric-visual.simple .change {
    font-size: 1rem;
    opacity: 0.9;
}

.metric-visual.gauge {
    text-align: center;
    padding: var(--spacing-lg);
}

.gauge-container {
    position: relative;
    width: 200px;
    height: 120px;
    margin: 0 auto var(--spacing-md);
}

.gauge-value {
    position: absolute;
    top: 70%;
    left: 50%;
    transform: translateX(-50%);
    font-size: 1.5rem;
    font-weight: var(--font-weight-bold);
    color: var(--text-primary);
}

/* Forecast bar styles */
.forecast-container {
    position: relative;
    margin: var(--spacing-lg) 0;
}

.forecast-bar {
    position: relative;
    height: 40px;
    background-color: var(--bg-secondary);
    border-radius: var(--radius-md);
    overflow: hidden;
    cursor: pointer;
}

.forecast-bar.draggable {
    cursor: grab;
}

.forecast-bar.dragging {
    cursor: grabbing;
}

.forecast-actual {
    height: 100%;
    background: var(--accent-gradient);
    transition: width var(--transition-normal);
    position: relative;
}

.forecast-predicted {
    position: absolute;
    top: 0;
    height: 100%;
    background: repeating-linear-gradient(
        45deg,
        rgba(59, 130, 246, 0.3),
        rgba(59, 130, 246, 0.3) 10px,
        rgba(99, 102, 241, 0.3) 10px,
        rgba(99, 102, 241, 0.3) 20px
    );
    border: 2px dashed var(--accent-primary);
    border-radius: var(--radius-md);
}

.forecast-handle {
    position: absolute;
    right: -8px;
    top: 50%;
    transform: translateY(-50%);
    width: 16px;
    height: 24px;
    background-color: var(--accent-primary);
    border-radius: var(--radius-sm);
    cursor: ew-resize;
    transition: all var(--transition-fast);
}

.forecast-handle:hover {
    background-color: var(--accent-secondary);
    transform: translateY(-50%) scale(1.1);
}

.forecast-labels {
    display: flex;
    justify-content: space-between;
    margin-top: var(--spacing-xs);
    font-size: 0.75rem;
    color: var(--text-muted);
}

.forecast-tooltip {
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    background-color: var(--bg-primary);
    border: 1px solid var(--border-primary);
    border-radius: var(--radius-md);
    padding: var(--spacing-sm);
    font-size: 0.75rem;
    white-space: nowrap;
    box-shadow: var(--shadow-md);
    z-index: 10;
}

/* Search and autocomplete styles */
.search-container {
    position: relative;
}

.search-results {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background-color: var(--bg-primary);
    border: 1px solid var(--border-primary);
    border-top: none;
    border-radius: 0 0 var(--radius-md) var(--radius-md);
    max-height: 200px;
    overflow-y: auto;
    z-index: 10;
    box-shadow: var(--shadow-md);
}

.search-result {
    padding: var(--spacing-sm) var(--spacing-md);
    cursor: pointer;
    transition: background-color var(--transition-fast);
    border-bottom: 1px solid var(--border-primary);
}

.search-result:last-child {
    border-bottom: none;
}

.search-result:hover {
    background-color: var(--bg-tertiary);
}

.search-result.selected {
    background-color: var(--accent-primary);
    color: white;
}

.search-result-title {
    font-weight: var(--font-weight-medium);
    margin-bottom: var(--spacing-xs);
}

.search-result-meta {
    font-size: 0.75rem;
    color: var(--text-muted);
}

.search-result.selected .search-result-meta {
    color: rgba(255, 255, 255, 0.8);
}

/* Alert and notification styles */
.alert {
    padding: var(--spacing-md);
    border-radius: var(--radius-md);
    margin-bottom: var(--spacing-md);
    border: 1px solid transparent;
}

.alert-success {
    background-color: var(--success-light);
    border-color: var(--success);
    color: var(--success-dark);
}

.alert-warning {
    background-color: var(--warning-light);
    border-color: var(--warning);
    color: var(--warning-dark);
}

.alert-danger {
    background-color: var(--danger-light);
    border-color: var(--danger);
    color: var(--danger-dark);
}

.alert-info {
    background-color: var(--info-light);
    border-color: var(--info);
    color: var(--info-dark);
}

[data-theme="dark"] .alert-success {
    background-color: rgba(16, 185, 129, 0.1);
    color: var(--success);
}

[data-theme="dark"] .alert-warning {
    background-color: rgba(245, 158, 11, 0.1);
    color: var(--warning);
}

[data-theme="dark"] .alert-danger {
    background-color: rgba(239, 68, 68, 0.1);
    color: var(--danger);
}

[data-theme="dark"] .alert-info {
    background-color: rgba(6, 182, 212, 0.1);
    color: var(--info);
}

/* Progress and loading styles */
.progress {
    width: 100%;
    height: 8px;
    background-color: var(--bg-secondary);
    border-radius: var(--radius-sm);
    overflow: hidden;
}

.progress-bar {
    height: 100%;
    background: var(--accent-gradient);
    transition: width var(--transition-normal);
}

.loading-dots {
    display: inline-flex;
    gap: 4px;
}

.loading-dots .dot {
    width: 6px;
    height: 6px;
    border-radius: 50%;
    background-color: var(--accent-primary);
    animation: loading-dots 1.4s infinite ease-in-out;
}

.loading-dots .dot:nth-child(1) { animation-delay: -0.32s; }
.loading-dots .dot:nth-child(2) { animation-delay: -0.16s; }

@keyframes loading-dots {
    0%, 80%, 100% {
        transform: scale(0);
        opacity: 0.5;
    }
    40% {
        transform: scale(1);
        opacity: 1;
    }
}

/* Responsive utilities */
@media (max-width: 1024px) {
    :root {
        --container-max-width: 100%;
        --sidebar-width: 250px;
    }
}

@media (max-width: 768px) {
    :root {
        --spacing-xs: 0.125rem;
        --spacing-sm: 0.375rem;
        --spacing-md: 0.75rem;
        --spacing-lg: 1.125rem;
        --spacing-xl: 1.5rem;
        --spacing-2xl: 2rem;
    }
    
    .modal {
        margin: var(--spacing-md);
        width: calc(100% - 2rem);
    }
    
    .btn {
        padding: var(--spacing-md) var(--spacing-lg);
        font-size: 1rem;
    }
    
    .btn-sm {
        padding: var(--spacing-sm) var(--spacing-md);
        font-size: 0.875rem;
    }
}

/* Print styles */
@media print {
    .navbar,
    .add-watch-btn,
    .modal-overlay,
    .notification-badge {
        display: none !important;
    }
    
    .main-content {
        max-width: none;
        padding: 0;
    }
    
    .card {
        break-inside: avoid;
        box-shadow: none;
        border: 1px solid #000;
    }
}