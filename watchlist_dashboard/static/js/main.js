/**
 * Stock Watchlist Dashboard - Main JavaScript
 * 
 * This file contains all the core functionality for the watchlist dashboard:
 * - Company search and autocomplete
 * - Metric visualization
 * - Forecast bars with drag functionality
 * - Real-time data updates
 * - Modal management
 * - Chart rendering
 */

// Global state
let currentSearchTimeout;
let currentUser = 'default';
let isDataLoading = false;
let searchCache = new Map();

// Initialize the application
document.addEventListener('DOMContentLoaded', function() {
    initializeApp();
});

function initializeApp() {
    // Set up event listeners
    setupEventListeners();
    
    // Initialize components
    initializeCharts();
    initializeSearches();
    
    // Load initial data
    loadInitialData();
    
    console.log('Stock Watchlist Dashboard initialized');
}

function setupEventListeners() {
    // Global keyboard shortcuts
    document.addEventListener('keydown', handleKeyboardShortcuts);
    
    // Modal close on escape
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape') {
            closeAllModals();
        }
    });
    
    // Click outside modal to close
    document.addEventListener('click', function(e) {
        if (e.target.classList.contains('modal-overlay')) {
            closeAllModals();
        }
    });
}

function handleKeyboardShortcuts(e) {
    // Ctrl/Cmd + K to open search
    if ((e.ctrlKey || e.metaKey) && e.key === 'k') {
        e.preventDefault();
        showAddWatchModal();
    }
    
    // Escape to close modals
    if (e.key === 'Escape') {
        closeAllModals();
    }
}

// Company Search Functionality
class CompanySearch {
    constructor(inputElement, resultsElement, onSelect) {
        this.input = inputElement;
        this.results = resultsElement;
        this.onSelect = onSelect;
        this.selectedIndex = -1;
        this.currentResults = [];
        
        this.setupEventListeners();
    }
    
    setupEventListeners() {
        this.input.addEventListener('input', (e) => this.handleInput(e));
        this.input.addEventListener('keydown', (e) => this.handleKeyDown(e));
        this.input.addEventListener('focus', () => this.showResults());
        this.input.addEventListener('blur', () => {
            // Delay hiding to allow for clicks
            setTimeout(() => this.hideResults(), 150);
        });
    }
    
    async handleInput(e) {
        const query = e.target.value.trim();
        
        if (query.length < 2) {
            this.hideResults();
            return;
        }
        
        // Debounce search
        clearTimeout(currentSearchTimeout);
        currentSearchTimeout = setTimeout(() => {
            this.performSearch(query);
        }, 300);
    }
    
    async performSearch(query) {
        try {
            // Check cache first
            if (searchCache.has(query)) {
                this.displayResults(searchCache.get(query));
                return;
            }
            
            const response = await fetch(`/search/companies?q=${encodeURIComponent(query)}`);
            const results = await response.json();
            
            // Cache results
            searchCache.set(query, results);
            
            this.displayResults(results);
        } catch (error) {
            console.error('Search error:', error);
            this.displayError('Search failed. Please try again.');
        }
    }
    
    displayResults(results) {
        this.currentResults = results;
        this.selectedIndex = -1;
        
        if (results.length === 0) {
            this.results.innerHTML = '<div class="search-result">No companies found</div>';
        } else {
            this.results.innerHTML = results.map((company, index) => `
                <div class="search-result" data-index="${index}" onclick="companySearch.selectResult(${index})">
                    <div class="search-result-title">${company.display_name}</div>
                    <div class="search-result-meta">${company.exchange} • ${company.country} • ${company.currency}</div>
                </div>
            `).join('');
        }
        
        this.showResults();
    }
    
    displayError(message) {
        this.results.innerHTML = `<div class="search-result text-danger">${message}</div>`;
        this.showResults();
    }
    
    handleKeyDown(e) {
        if (!this.results.classList.contains('hidden')) {
            switch (e.key) {
                case 'ArrowDown':
                    e.preventDefault();
                    this.selectedIndex = Math.min(this.selectedIndex + 1, this.currentResults.length - 1);
                    this.updateSelection();
                    break;
                case 'ArrowUp':
                    e.preventDefault();
                    this.selectedIndex = Math.max(this.selectedIndex - 1, -1);
                    this.updateSelection();
                    break;
                case 'Enter':
                    e.preventDefault();
                    if (this.selectedIndex >= 0) {
                        this.selectResult(this.selectedIndex);
                    }
                    break;
            }
        }
    }
    
    updateSelection() {
        const resultElements = this.results.querySelectorAll('.search-result');
        resultElements.forEach((el, index) => {
            el.classList.toggle('selected', index === this.selectedIndex);
        });
    }
    
    selectResult(index) {
        if (index >= 0 && index < this.currentResults.length) {
            const selected = this.currentResults[index];
            this.input.value = selected.display_name;
            this.hideResults();
            this.onSelect(selected);
        }
    }
    
    showResults() {
        this.results.classList.remove('hidden');
    }
    
    hideResults() {
        this.results.classList.add('hidden');
    }
    
    clear() {
        this.input.value = '';
        this.hideResults();
        this.currentResults = [];
        this.selectedIndex = -1;
    }
}

// Metric Selection and Management
class MetricSelector {
    constructor(containerElement, selectedMetrics = []) {
        this.container = containerElement;
        this.selectedMetrics = new Set(selectedMetrics);
        this.availableMetrics = [];
        
        this.loadAvailableMetrics();
    }
    
    async loadAvailableMetrics() {
        try {
            const response = await fetch('/search/metrics');
            this.availableMetrics = await response.json();
            this.render();
        } catch (error) {
            console.error('Error loading metrics:', error);
        }
    }
    
    render() {
        if (!this.availableMetrics.length) {
            this.container.innerHTML = '<div class="text-muted">Loading metrics...</div>';
            return;
        }
        
        // Group metrics by category
        const categories = {};
        this.availableMetrics.forEach(metric => {
            if (!categories[metric.category]) {
                categories[metric.category] = [];
            }
            categories[metric.category].push(metric);
        });
        
        this.container.innerHTML = Object.entries(categories).map(([category, metrics]) => `
            <div class="metric-category mb-4">
                <h4 class="text-primary mb-2">${category.charAt(0).toUpperCase() + category.slice(1)} Metrics</h4>
                <div class="metric-grid" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: var(--spacing-sm);">
                    ${metrics.map(metric => `
                        <label class="metric-option" style="display: flex; align-items: flex-start; gap: var(--spacing-sm); padding: var(--spacing-sm); border: 1px solid var(--border-primary); border-radius: var(--radius-md); cursor: pointer; transition: all var(--transition-fast);">
                            <input type="checkbox" 
                                   value="${metric.name}" 
                                   ${this.selectedMetrics.has(metric.name) ? 'checked' : ''}
                                   onchange="metricSelector.toggleMetric('${metric.name}', this.checked)"
                                   style="margin-top: 2px;">
                            <div style="flex: 1;">
                                <div style="font-weight: 600; margin-bottom: 2px;">${metric.display_name}</div>
                                <div style="font-size: 0.75rem; color: var(--text-muted); margin-bottom: 4px;">${metric.description}</div>
                                <div style="display: flex; gap: var(--spacing-xs);">
                                    <span style="font-size: 0.625rem; padding: 2px 6px; background-color: var(--accent-primary); color: white; border-radius: var(--radius-sm);">${metric.visualization}</span>
                                    <span style="font-size: 0.625rem; padding: 2px 6px; background-color: var(--bg-tertiary); color: var(--text-secondary); border-radius: var(--radius-sm);">${metric.format}</span>
                                </div>
                            </div>
                        </label>
                    `).join('')}
                </div>
            </div>
        `).join('');
        
        // Add hover effects
        this.container.querySelectorAll('.metric-option').forEach(option => {
            option.addEventListener('mouseenter', function() {
                this.style.borderColor = 'var(--accent-primary)';
                this.style.backgroundColor = 'var(--bg-secondary)';
            });
            option.addEventListener('mouseleave', function() {
                this.style.borderColor = 'var(--border-primary)';
                this.style.backgroundColor = 'transparent';
            });
        });
    }
    
    toggleMetric(metricName, isSelected) {
        if (isSelected) {
            this.selectedMetrics.add(metricName);
        } else {
            this.selectedMetrics.delete(metricName);
            // Also remove any conditions for this metric
            this.removeConditionsForMetric(metricName);
        }
        
        this.updateConditionBuilder();
    }
    
    updateConditionBuilder() {
        const conditionContainer = document.getElementById('conditionBuilder');
        if (!conditionContainer) return;
        
        if (this.selectedMetrics.size === 0) {
            conditionContainer.innerHTML = '<div class="text-muted">Select metrics above to set alert conditions</div>';
            return;
        }
        
        conditionContainer.innerHTML = Array.from(this.selectedMetrics).map(metricName => {
            const metric = this.availableMetrics.find(m => m.name === metricName);
            if (!metric) return '';
            
            return `
                <div class="condition-item mb-3 p-3 border rounded">
                    <h5>${metric.display_name}</h5>
                    <div class="row">
                        <div class="col-md-4">
                            <select class="form-input" data-metric="${metricName}" data-field="condition">
                                <option value="">No alert</option>
                                <option value="greater_than">Greater than</option>
                                <option value="less_than">Less than</option>
                                <option value="equals">Equals</option>
                                <option value="crosses_above">Crosses above</option>
                                <option value="crosses_below">Crosses below</option>
                            </select>
                        </div>
                        <div class="col-md-4">
                            <input type="number" 
                                   class="form-input" 
                                   placeholder="Target value" 
                                   data-metric="${metricName}" 
                                   data-field="target_value"
                                   step="0.01">
                        </div>
                        <div class="col-md-4">
                            <input type="text" 
                                   class="form-input" 
                                   placeholder="Note (optional)" 
                                   data-metric="${metricName}" 
                                   data-field="description">
                        </div>
                    </div>
                </div>
            `;
        }).join('');
    }
    
    removeConditionsForMetric(metricName) {
        const conditionItems = document.querySelectorAll(`[data-metric="${metricName}"]`);
        conditionItems.forEach(item => {
            const container = item.closest('.condition-item');
            if (container) container.remove();
        });
    }
    
    getSelectedMetrics() {
        return Array.from(this.selectedMetrics);
    }
    
    getConditions() {
        const conditions = {};
        const conditionItems = document.querySelectorAll('.condition-item');
        
        conditionItems.forEach(item => {
            const metricName = item.querySelector('[data-field="condition"]').dataset.metric;
            const conditionType = item.querySelector('[data-field="condition"]').value;
            const targetValue = item.querySelector('[data-field="target_value"]').value;
            const description = item.querySelector('[data-field="description"]').value;
            
            if (conditionType && targetValue) {
                conditions[metricName] = {
                    condition_type: conditionType,
                    target_value: parseFloat(targetValue),
                    description: description || ''
                };
            }
        });
        
        return conditions;
    }
}

// Chart Management
class ChartManager {
    constructor() {
        this.charts = new Map();
        this.defaultOptions = {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                },
                tooltip: {
                    mode: 'index',
                    intersect: false,
                }
            },
            scales: {
                x: {
                    display: true,
                    grid: {
                        display: false
                    }
                },
                y: {
                    display: true,
                    grid: {
                        color: 'rgba(0, 0, 0, 0.1)'
                    }
                }
            },
            elements: {
                line: {
                    tension: 0.4
                },
                point: {
                    radius: 0,
                    hoverRadius: 6
                }
            }
        };
    }
    
    createSimpleMetricChart(containerId, data) {
        const ctx = document.getElementById(containerId)?.getContext('2d');
        if (!ctx) return null;
        
        const chart = new Chart(ctx, {
            type: 'line',
            data: {
                labels: data.map(d => d.date),
                datasets: [{
                    data: data.map(d => d.value),
                    borderColor: '#3b82f6',
                    backgroundColor: 'rgba(59, 130, 246, 0.1)',
                    fill: true
                }]
            },
            options: {
                ...this.defaultOptions,
                scales: {
                    ...this.defaultOptions.scales,
                    y: {
                        ...this.defaultOptions.scales.y,
                        beginAtZero: false
                    }
                }
            }
        });
        
        this.charts.set(containerId, chart);
        return chart;
    }
    
    createGaugeChart(containerId, value, min = 0, max = 100) {
        const container = document.getElementById(containerId);
        if (!container) return null;
        
        // Create SVG gauge
        const svg = d3.select(container)
            .append('svg')
            .attr('width', 200)
            .attr('height', 120);
        
        const width = 200;
        const height = 120;
        const radius = Math.min(width, height * 2) / 2 - 10;
        
        const g = svg.append('g')
            .attr('transform', `translate(${width/2}, ${height - 10})`);
        
        // Create arc generator
        const arc = d3.arc()
            .innerRadius(radius - 20)
            .outerRadius(radius);
        
        // Background arc
        g.append('path')
            .datum({startAngle: -Math.PI/2, endAngle: Math.PI/2})
            .attr('d', arc)
            .attr('fill', '#e5e7eb');
        
        // Value arc
        const valueAngle = -Math.PI/2 + (value - min) / (max - min) * Math.PI;
        g.append('path')
            .datum({startAngle: -Math.PI/2, endAngle: valueAngle})
            .attr('d', arc)
            .attr('fill', this.getGaugeColor(value, min, max));
        
        // Value text
        g.append('text')
            .attr('text-anchor', 'middle')
            .attr('dy', '-1em')
            .style('font-size', '24px')
            .style('font-weight', 'bold')
            .style('fill', '#374151')
            .text(value.toFixed(1));
        
        return svg.node();
    }
    
    getGaugeColor(value, min, max) {
        const ratio = (value - min) / (max - min);
        if (ratio < 0.3) return '#ef4444';
        if (ratio < 0.7) return '#f59e0b';
        return '#10b981';
    }
    
    createForecastChart(containerId, historicalData, forecasts) {
        const ctx = document.getElementById(containerId)?.getContext('2d');
        if (!ctx) return null;
        
        const chart = new Chart(ctx, {
            type: 'bar',
            data: {
                labels: [...historicalData.map(d => d.date), ...forecasts.map(f => f.date)],
                datasets: [
                    {
                        label: 'Historical',
                        data: historicalData.map(d => d.value),
                        backgroundColor: '#3b82f6',
                        borderColor: '#3b82f6'
                    },
                    {
                        label: 'Forecast',
                        data: [
                            ...new Array(historicalData.length).fill(null),
                            ...forecasts.map(f => f.value)
                        ],
                        backgroundColor: 'rgba(99, 102, 241, 0.5)',
                        borderColor: '#6366f1',
                        borderStyle: 'dashed'
                    }
                ]
            },
            options: {
                ...this.defaultOptions,
                plugins: {
                    ...this.defaultOptions.plugins,
                    legend: {
                        display: true
                    }
                }
            }
        });
        
        this.charts.set(containerId, chart);
        return chart;
    }
    
    destroyChart(containerId) {
        const chart = this.charts.get(containerId);
        if (chart) {
            chart.destroy();
            this.charts.delete(containerId);
        }
    }
    
    destroyAllCharts() {
        this.charts.forEach(chart => chart.destroy());
        this.charts.clear();
    }
}

// Forecast Bar Component
class ForecastBar {
    constructor(container, data) {
        this.container = container;
        this.data = data;
        this.isDragging = false;
        this.forecastValue = data.forecast_value || 0;
        this.maxValue = data.max_value || 1000;
        
        this.render();
        this.setupEventListeners();
    }
    
    render() {
        const actualPercent = (this.data.actual_value / this.maxValue) * 100;
        const forecastPercent = (this.forecastValue / this.maxValue) * 100;
        
        this.container.innerHTML = `
            <div class="forecast-container">
                <div class="forecast-bar" id="forecastBar">
                    <div class="forecast-actual" style="width: ${actualPercent}%"></div>
                    <div class="forecast-predicted" style="left: ${Math.min(actualPercent, forecastPercent)}%; width: ${Math.abs(forecastPercent - actualPercent)}%">
                        <div class="forecast-handle" id="forecastHandle"></div>
                    </div>
                </div>
                <div class="forecast-labels">
                    <span>$0</span>
                    <span>Actual: ${this.formatCurrency(this.data.actual_value)}</span>
                    <span>Forecast: ${this.formatCurrency(this.forecastValue)}</span>
                    <span>${this.formatCurrency(this.maxValue)}</span>
                </div>
            </div>
        `;
    }
    
    setupEventListeners() {
        const handle = this.container.querySelector('#forecastHandle');
        const bar = this.container.querySelector('#forecastBar');
        
        if (!handle || !bar) return;
        
        handle.addEventListener('mousedown', (e) => this.startDragging(e));
        document.addEventListener('mousemove', (e) => this.drag(e));
        document.addEventListener('mouseup', () => this.stopDragging());
        
        // Touch events for mobile
        handle.addEventListener('touchstart', (e) => this.startDragging(e.touches[0]));
        document.addEventListener('touchmove', (e) => this.drag(e.touches[0]));
        document.addEventListener('touchend', () => this.stopDragging());
    }
    
    startDragging(e) {
        this.isDragging = true;
        this.container.querySelector('#forecastBar').classList.add('dragging');
        e.preventDefault();
    }
    
    drag(e) {
        if (!this.isDragging) return;
        
        const bar = this.container.querySelector('#forecastBar');
        const rect = bar.getBoundingClientRect();
        const x = e.clientX - rect.left;
        const percent = Math.max(0, Math.min(100, (x / rect.width) * 100));
        
        this.forecastValue = (percent / 100) * this.maxValue;
        this.updateDisplay();
    }
    
    stopDragging() {
        if (!this.isDragging) return;
        
        this.isDragging = false;
        this.container.querySelector('#forecastBar').classList.remove('dragging');
        
        // Trigger change event
        this.onForecastChange?.(this.forecastValue);
    }
    
    updateDisplay() {
        const actualPercent = (this.data.actual_value / this.maxValue) * 100;
        const forecastPercent = (this.forecastValue / this.maxValue) * 100;
        
        const predicted = this.container.querySelector('.forecast-predicted');
        predicted.style.left = `${Math.min(actualPercent, forecastPercent)}%`;
        predicted.style.width = `${Math.abs(forecastPercent - actualPercent)}%`;
        
        const labels = this.container.querySelector('.forecast-labels');
        labels.children[2].textContent = `Forecast: ${this.formatCurrency(this.forecastValue)}`;
    }
    
    formatCurrency(value) {
        const absValue = Math.abs(value);
        if (absValue >= 1e9) return `$${(value / 1e9).toFixed(2)}B`;
        if (absValue >= 1e6) return `$${(value / 1e6).toFixed(2)}M`;
        if (absValue >= 1e3) return `$${(value / 1e3).toFixed(2)}K`;
        return `$${value.toFixed(2)}`;
    }
    
    getValue() {
        return this.forecastValue;
    }
    
    setValue(value) {
        this.forecastValue = value;
        this.updateDisplay();
    }
}

// Modal Management
function showAddWatchModal() {
    const modalHtml = `
        <div class="modal-overlay" id="addWatchModalOverlay">
            <div class="modal">
                <div class="modal-header">
                    <h2 class="modal-title">Add Stock to Watchlist</h2>
                    <button class="modal-close" onclick="closeAddWatchModal()">&times;</button>
                </div>
                <div class="modal-body">
                    <form id="addWatchForm">
                        <div class="form-group">
                            <label class="form-label">Search Company</label>
                            <div class="search-container">
                                <input type="text" 
                                       id="companySearch" 
                                       class="form-input" 
                                       placeholder="Type company name or ticker..."
                                       autocomplete="off">
                                <div id="companyResults" class="search-results hidden"></div>
                            </div>
                        </div>
                        
                        <div class="form-group">
                            <label class="form-label">Select Metrics to Track</label>
                            <div id="metricSelector" class="metric-selector">
                                Loading available metrics...
                            </div>
                        </div>
                        
                        <div class="form-group">
                            <label class="form-label">Set Alert Conditions</label>
                            <div id="conditionBuilder">
                                Select metrics above to set alert conditions
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button class="btn btn-secondary" onclick="closeAddWatchModal()">Cancel</button>
                    <button class="btn btn-primary" onclick="saveWatchlist()">Add to Watchlist</button>
                </div>
            </div>
        </div>
    `;
    
    document.body.insertAdjacentHTML('beforeend', modalHtml);
    
    // Initialize components
    setTimeout(() => {
        const overlay = document.getElementById('addWatchModalOverlay');
        overlay.classList.add('show');
        
        // Initialize company search
        const companyInput = document.getElementById('companySearch');
        const companyResults = document.getElementById('companyResults');
        window.companySearch = new CompanySearch(
            companyInput, 
            companyResults, 
            (company) => {
                selectedCompany = company;
                console.log('Selected company:', company);
            }
        );
        
        // Initialize metric selector
        const metricContainer = document.getElementById('metricSelector');
        window.metricSelector = new MetricSelector(metricContainer);
        
        companyInput.focus();
    }, 10);
}

function closeAddWatchModal() {
    const overlay = document.getElementById('addWatchModalOverlay');
    if (overlay) {
        overlay.classList.remove('show');
        setTimeout(() => overlay.remove(), 300);
    }
}

function closeAllModals() {
    const modals = document.querySelectorAll('.modal-overlay');
    modals.forEach(modal => {
        modal.classList.remove('show');
        setTimeout(() => modal.remove(), 300);
    });
}

async function saveWatchlist() {
    if (!window.selectedCompany) {
        alert('Please select a company first');
        return;
    }
    
    const selectedMetrics = window.metricSelector.getSelectedMetrics();
    if (selectedMetrics.length === 0) {
        alert('Please select at least one metric to track');
        return;
    }
    
    const conditions = window.metricSelector.getConditions();
    
    try {
        // Save each metric separately
        for (const metric of selectedMetrics) {
            const condition = conditions[metric] || {};
            
            const response = await fetch('/watchlist', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    ticker: window.selectedCompany.ticker,
                    company_name: window.selectedCompany.name,
                    metric: metric,
                    condition: condition.condition_type || '',
                    target_value: condition.target_value || null,
                    description: condition.description || ''
                })
            });
            
            if (!response.ok) {
                throw new Error('Failed to save watchlist item');
            }
        }
        
        // Success - reload page
        closeAddWatchModal();
        location.reload();
        
    } catch (error) {
        console.error('Error saving watchlist:', error);
        alert('Failed to save watchlist. Please try again.');
    }
}

// Global variables
let selectedCompany = null;
let companySearch = null;
let metricSelector = null;
let chartManager = new ChartManager();

// Utility functions
function initializeCharts() {
    // Any initial chart setup
}

function initializeSearches() {
    // Any initial search setup
}

function loadInitialData() {
    // Load any initial data needed
}

// Export for global access
window.showAddWatchModal = showAddWatchModal;
window.closeAddWatchModal = closeAddWatchModal;
window.saveWatchlist = saveWatchlist;