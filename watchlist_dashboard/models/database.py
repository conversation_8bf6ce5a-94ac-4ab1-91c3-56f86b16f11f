"""
Database models and initialization for Stock Watchlist Dashboard

This module handles all database operations using SQLite with a simple
schema for watchlists, metrics, notifications, and user forecasts.
"""

import sqlite3
import os
import logging
from datetime import datetime
from contextlib import contextmanager
from typing import Dict, List, Optional, Any

logger = logging.getLogger(__name__)

# Database file path
DB_PATH = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'watchlist.db')

def init_db():
    """Initialize the database with all required tables"""
    try:
        with sqlite3.connect(DB_PATH) as conn:
            cursor = conn.cursor()
            
            # Create watchlist_items table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS watchlist_items (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_id TEXT NOT NULL,
                    ticker TEXT NOT NULL,
                    company_name TEXT,
                    metric_type TEXT NOT NULL,
                    metric_name TEXT NOT NULL,
                    condition_type TEXT NOT NULL,
                    target_value REAL,
                    description TEXT,
                    is_active INTEGER DEFAULT 1,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                );
            ''')
            
            # Create user_forecasts table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS user_forecasts (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_id TEXT NOT NULL,
                    ticker TEXT NOT NULL,
                    metric_name TEXT NOT NULL,
                    forecast_value REAL NOT NULL,
                    forecast_date TEXT NOT NULL,
                    description TEXT,
                    actual_value REAL,
                    achieved_date TEXT,
                    is_achieved INTEGER DEFAULT 0,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                );
            ''')
            
            # Create notifications table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS notifications (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_id TEXT NOT NULL,
                    ticker TEXT NOT NULL,
                    metric_name TEXT NOT NULL,
                    message TEXT NOT NULL,
                    notification_type TEXT NOT NULL,
                    is_read INTEGER DEFAULT 0,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                );
            ''')
            
            # Create metric_cache table for caching API responses
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS metric_cache (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    ticker TEXT NOT NULL,
                    metric_name TEXT NOT NULL,
                    data TEXT NOT NULL,
                    cached_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    expires_at TIMESTAMP NOT NULL,
                    UNIQUE(ticker, metric_name)
                );
            ''')
            
            # Create indexes for better performance
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_watchlist_user ON watchlist_items(user_id);')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_watchlist_ticker ON watchlist_items(ticker);')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_forecasts_user ON user_forecasts(user_id);')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_notifications_user ON notifications(user_id);')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_cache_ticker_metric ON metric_cache(ticker, metric_name);')
            
            conn.commit()
            logger.info("Database initialized successfully")
            
    except Exception as e:
        logger.error(f"Error initializing database: {e}")
        raise

@contextmanager
def get_db():
    """Context manager for database connections"""
    conn = None
    try:
        conn = sqlite3.connect(DB_PATH)
        conn.row_factory = sqlite3.Row  # Enable column access by name
        yield conn
    except Exception as e:
        if conn:
            conn.rollback()
        logger.error(f"Database error: {e}")
        raise
    finally:
        if conn:
            conn.close()

class WatchlistItemModel:
    """Model for watchlist items"""
    
    @staticmethod
    def create(user_id: str, ticker: str, company_name: str, metric_type: str, 
              metric_name: str, condition_type: str, target_value: float = None, 
              description: str = '') -> Optional[int]:
        """Create a new watchlist item"""
        try:
            with get_db() as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    INSERT INTO watchlist_items 
                    (user_id, ticker, company_name, metric_type, metric_name, 
                     condition_type, target_value, description)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                ''', (user_id, ticker, company_name, metric_type, metric_name, 
                      condition_type, target_value, description))
                conn.commit()
                return cursor.lastrowid
        except Exception as e:
            logger.error(f"Error creating watchlist item: {e}")
            return None
    
    @staticmethod
    def get_by_user(user_id: str) -> List[Dict[str, Any]]:
        """Get all watchlist items for a user"""
        try:
            with get_db() as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    SELECT * FROM watchlist_items 
                    WHERE user_id = ? AND is_active = 1
                    ORDER BY created_at DESC
                ''', (user_id,))
                rows = cursor.fetchall()
                return [dict(row) for row in rows]
        except Exception as e:
            logger.error(f"Error getting watchlist items: {e}")
            return []
    
    @staticmethod
    def get_by_ticker(user_id: str, ticker: str) -> List[Dict[str, Any]]:
        """Get watchlist items for a specific ticker"""
        try:
            with get_db() as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    SELECT * FROM watchlist_items 
                    WHERE user_id = ? AND ticker = ? AND is_active = 1
                    ORDER BY created_at DESC
                ''', (user_id, ticker))
                rows = cursor.fetchall()
                return [dict(row) for row in rows]
        except Exception as e:
            logger.error(f"Error getting watchlist items for ticker: {e}")
            return []
    
    @staticmethod
    def delete(user_id: str, item_id: int) -> bool:
        """Delete (deactivate) a watchlist item"""
        try:
            with get_db() as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    UPDATE watchlist_items 
                    SET is_active = 0, updated_at = CURRENT_TIMESTAMP
                    WHERE id = ? AND user_id = ?
                ''', (item_id, user_id))
                conn.commit()
                return cursor.rowcount > 0
        except Exception as e:
            logger.error(f"Error deleting watchlist item: {e}")
            return False

class ForecastModel:
    """Model for user forecasts"""
    
    @staticmethod
    def create(user_id: str, ticker: str, metric_name: str, forecast_value: float,
              forecast_date: str, description: str = '') -> Optional[int]:
        """Create a new forecast"""
        try:
            with get_db() as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    INSERT INTO user_forecasts 
                    (user_id, ticker, metric_name, forecast_value, forecast_date, description)
                    VALUES (?, ?, ?, ?, ?, ?)
                ''', (user_id, ticker, metric_name, forecast_value, forecast_date, description))
                conn.commit()
                return cursor.lastrowid
        except Exception as e:
            logger.error(f"Error creating forecast: {e}")
            return None
    
    @staticmethod
    def get_by_user(user_id: str) -> List[Dict[str, Any]]:
        """Get all forecasts for a user"""
        try:
            with get_db() as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    SELECT * FROM user_forecasts 
                    WHERE user_id = ?
                    ORDER BY forecast_date ASC
                ''', (user_id,))
                rows = cursor.fetchall()
                return [dict(row) for row in rows]
        except Exception as e:
            logger.error(f"Error getting forecasts: {e}")
            return []
    
    @staticmethod
    def get_by_ticker_metric(user_id: str, ticker: str, metric_name: str) -> List[Dict[str, Any]]:
        """Get forecasts for a specific ticker and metric"""
        try:
            with get_db() as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    SELECT * FROM user_forecasts 
                    WHERE user_id = ? AND ticker = ? AND metric_name = ?
                    ORDER BY forecast_date ASC
                ''', (user_id, ticker, metric_name))
                rows = cursor.fetchall()
                return [dict(row) for row in rows]
        except Exception as e:
            logger.error(f"Error getting forecasts for ticker/metric: {e}")
            return []
    
    @staticmethod
    def update_achievement(forecast_id: int, actual_value: float, achieved_date: str) -> bool:
        """Update forecast with actual achievement data"""
        try:
            with get_db() as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    UPDATE user_forecasts 
                    SET actual_value = ?, achieved_date = ?, is_achieved = 1
                    WHERE id = ?
                ''', (actual_value, achieved_date, forecast_id))
                conn.commit()
                return cursor.rowcount > 0
        except Exception as e:
            logger.error(f"Error updating forecast achievement: {e}")
            return False

class NotificationModel:
    """Model for notifications"""
    
    @staticmethod
    def create(user_id: str, ticker: str, metric_name: str, message: str, 
              notification_type: str = 'alert') -> Optional[int]:
        """Create a new notification"""
        try:
            with get_db() as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    INSERT INTO notifications 
                    (user_id, ticker, metric_name, message, notification_type)
                    VALUES (?, ?, ?, ?, ?)
                ''', (user_id, ticker, metric_name, message, notification_type))
                conn.commit()
                return cursor.lastrowid
        except Exception as e:
            logger.error(f"Error creating notification: {e}")
            return None
    
    @staticmethod
    def get_by_user(user_id: str, limit: int = 50) -> List[Dict[str, Any]]:
        """Get notifications for a user"""
        try:
            with get_db() as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    SELECT * FROM notifications 
                    WHERE user_id = ?
                    ORDER BY created_at DESC
                    LIMIT ?
                ''', (user_id, limit))
                rows = cursor.fetchall()
                return [dict(row) for row in rows]
        except Exception as e:
            logger.error(f"Error getting notifications: {e}")
            return []
    
    @staticmethod
    def mark_as_read(user_id: str, notification_id: int) -> bool:
        """Mark a notification as read"""
        try:
            with get_db() as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    UPDATE notifications 
                    SET is_read = 1
                    WHERE id = ? AND user_id = ?
                ''', (notification_id, user_id))
                conn.commit()
                return cursor.rowcount > 0
        except Exception as e:
            logger.error(f"Error marking notification as read: {e}")
            return False
    
    @staticmethod
    def get_unread_count(user_id: str) -> int:
        """Get count of unread notifications"""
        try:
            with get_db() as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    SELECT COUNT(*) FROM notifications 
                    WHERE user_id = ? AND is_read = 0
                ''', (user_id,))
                return cursor.fetchone()[0]
        except Exception as e:
            logger.error(f"Error getting unread notification count: {e}")
            return 0

class CacheModel:
    """Model for caching API responses"""
    
    @staticmethod
    def set(ticker: str, metric_name: str, data: str, expires_at: datetime) -> bool:
        """Cache data for a ticker/metric combination"""
        try:
            with get_db() as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    INSERT OR REPLACE INTO metric_cache 
                    (ticker, metric_name, data, expires_at)
                    VALUES (?, ?, ?, ?)
                ''', (ticker, metric_name, data, expires_at.isoformat()))
                conn.commit()
                return True
        except Exception as e:
            logger.error(f"Error caching data: {e}")
            return False
    
    @staticmethod
    def get(ticker: str, metric_name: str) -> Optional[str]:
        """Get cached data if not expired"""
        try:
            with get_db() as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    SELECT data FROM metric_cache 
                    WHERE ticker = ? AND metric_name = ? 
                    AND expires_at > ?
                ''', (ticker, metric_name, datetime.utcnow().isoformat()))
                row = cursor.fetchone()
                return row[0] if row else None
        except Exception as e:
            logger.error(f"Error getting cached data: {e}")
            return None
    
    @staticmethod
    def clear_expired() -> int:
        """Clear expired cache entries"""
        try:
            with get_db() as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    DELETE FROM metric_cache 
                    WHERE expires_at <= ?
                ''', (datetime.utcnow().isoformat(),))
                conn.commit()
                return cursor.rowcount
        except Exception as e:
            logger.error(f"Error clearing expired cache: {e}")
            return 0