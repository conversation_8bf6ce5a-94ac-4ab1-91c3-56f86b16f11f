#!/usr/bin/env python3
"""
Stock Watchlist Dashboard - Main Flask Application

A visually stunning, highly interactive stock watchlist dashboard that integrates 
deeply with the EODHD API for comprehensive financial data tracking and analysis.
"""

import os
import logging
from datetime import datetime, timedelta
from flask import Flask, render_template, request, jsonify, session, redirect, url_for, flash
from flask_session import Session
from flask_caching import Cache
from dotenv import load_dotenv

# Import our services
from services.eodhd_service import EODHDService
from services.watchlist_service import WatchlistService
from services.notification_service import NotificationService
from models.database import init_db, get_db
from utils.helpers import format_currency, format_percentage

# Load environment variables
load_dotenv()

# Initialize Flask app
app = Flask(__name__)

# Configuration
app.config['SECRET_KEY'] = os.environ.get('SECRET_KEY', 'dev-secret-key-change-in-production')
app.config['SESSION_TYPE'] = 'filesystem'
app.config['SESSION_PERMANENT'] = False
app.config['SESSION_USE_SIGNER'] = True
app.config['CACHE_TYPE'] = 'simple'
app.config['CACHE_DEFAULT_TIMEOUT'] = 300

# Initialize extensions
Session(app)
cache = Cache(app)

# Initialize database
init_db()

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Initialize services
eodhd_service = EODHDService(os.environ.get('EODHD_API_KEY'))
watchlist_service = WatchlistService()
notification_service = NotificationService()

# Template filters
@app.template_filter('currency')
def currency_filter(value):
    """Format currency values"""
    return format_currency(value)

@app.template_filter('percentage')
def percentage_filter(value):
    """Format percentage values"""
    return format_percentage(value)

# Error handlers
@app.errorhandler(404)
def not_found_error(error):
    return render_template('errors/404.html'), 404

@app.errorhandler(500)
def internal_error(error):
    return render_template('errors/500.html'), 500

# Main routes
@app.route('/')
def index():
    """Main dashboard page"""
    try:
        # Get user's watchlists
        user_id = session.get('user_id', 'default')
        watchlists = watchlist_service.get_user_watchlists(user_id)
        
        # Get summary stats
        total_stocks = sum(len(w.get('stocks', [])) for w in watchlists)
        active_alerts = sum(len(w.get('alerts', [])) for w in watchlists)
        
        return render_template('dashboard.html', 
                             watchlists=watchlists,
                             total_stocks=total_stocks,
                             active_alerts=active_alerts)
    except Exception as e:
        logger.error(f"Error loading dashboard: {e}")
        flash('Error loading dashboard', 'error')
        return render_template('dashboard.html', watchlists=[])

@app.route('/search/companies')
def search_companies():
    """Search for companies using EODHD API"""
    query = request.args.get('q', '').strip()
    
    if not query or len(query) < 2:
        return jsonify([])
    
    try:
        results = eodhd_service.search_companies(query)
        return jsonify(results)
    except Exception as e:
        logger.error(f"Error searching companies: {e}")
        return jsonify([]), 500

@app.route('/search/metrics')
def search_metrics():
    """Get available metrics for a stock"""
    ticker = request.args.get('ticker', '').strip()
    
    if not ticker:
        return jsonify([])
    
    try:
        metrics = eodhd_service.get_available_metrics(ticker)
        return jsonify(metrics)
    except Exception as e:
        logger.error(f"Error getting metrics for {ticker}: {e}")
        return jsonify([]), 500

@app.route('/watchlist', methods=['POST'])
def add_to_watchlist():
    """Add a stock and metric to watchlist"""
    try:
        data = request.get_json()
        ticker = data.get('ticker')
        metric = data.get('metric')
        condition = data.get('condition')
        target_value = data.get('target_value')
        description = data.get('description', '')
        
        if not all([ticker, metric, condition]):
            return jsonify({'error': 'Missing required fields'}), 400
        
        user_id = session.get('user_id', 'default')
        result = watchlist_service.add_watch_item(
            user_id, ticker, metric, condition, target_value, description
        )
        
        if result:
            return jsonify({'success': True, 'message': 'Added to watchlist'})
        else:
            return jsonify({'error': 'Failed to add to watchlist'}), 500
            
    except Exception as e:
        logger.error(f"Error adding to watchlist: {e}")
        return jsonify({'error': 'Internal server error'}), 500

@app.route('/watchlist/<int:item_id>', methods=['DELETE'])
def remove_from_watchlist(item_id):
    """Remove item from watchlist"""
    try:
        user_id = session.get('user_id', 'default')
        result = watchlist_service.remove_watch_item(user_id, item_id)
        
        if result:
            return jsonify({'success': True, 'message': 'Removed from watchlist'})
        else:
            return jsonify({'error': 'Item not found'}), 404
            
    except Exception as e:
        logger.error(f"Error removing from watchlist: {e}")
        return jsonify({'error': 'Internal server error'}), 500

@app.route('/data/<ticker>/<metric>')
def get_metric_data():
    """Get current and historical data for a specific metric"""
    ticker = request.view_args['ticker']
    metric = request.view_args['metric']
    
    try:
        # Get current data
        current_data = eodhd_service.get_current_data(ticker, metric)
        
        # Get historical data
        historical_data = eodhd_service.get_historical_data(ticker, metric)
        
        return jsonify({
            'current': current_data,
            'historical': historical_data,
            'ticker': ticker,
            'metric': metric
        })
        
    except Exception as e:
        logger.error(f"Error getting data for {ticker}/{metric}: {e}")
        return jsonify({'error': 'Failed to fetch data'}), 500

@app.route('/forecast', methods=['POST'])
def save_forecast():
    """Save user forecast for a metric"""
    try:
        data = request.get_json()
        ticker = data.get('ticker')
        metric = data.get('metric')
        forecast_value = data.get('forecast_value')
        forecast_date = data.get('forecast_date')
        description = data.get('description', '')
        
        if not all([ticker, metric, forecast_value, forecast_date]):
            return jsonify({'error': 'Missing required fields'}), 400
            
        user_id = session.get('user_id', 'default')
        result = watchlist_service.save_forecast(
            user_id, ticker, metric, forecast_value, forecast_date, description
        )
        
        if result:
            return jsonify({'success': True, 'message': 'Forecast saved'})
        else:
            return jsonify({'error': 'Failed to save forecast'}), 500
            
    except Exception as e:
        logger.error(f"Error saving forecast: {e}")
        return jsonify({'error': 'Internal server error'}), 500

@app.route('/notifications')
def get_notifications():
    """Get user notifications"""
    try:
        user_id = session.get('user_id', 'default')
        notifications = notification_service.get_user_notifications(user_id)
        return jsonify(notifications)
    except Exception as e:
        logger.error(f"Error getting notifications: {e}")
        return jsonify([]), 500

@app.route('/notifications/<int:notification_id>/read', methods=['POST'])
def mark_notification_read(notification_id):
    """Mark notification as read"""
    try:
        user_id = session.get('user_id', 'default')
        result = notification_service.mark_as_read(user_id, notification_id)
        
        if result:
            return jsonify({'success': True})
        else:
            return jsonify({'error': 'Notification not found'}), 404
            
    except Exception as e:
        logger.error(f"Error marking notification as read: {e}")
        return jsonify({'error': 'Internal server error'}), 500

@app.route('/health')
def health_check():
    """Health check endpoint"""
    return jsonify({
        'status': 'healthy',
        'timestamp': datetime.utcnow().isoformat(),
        'version': '1.0.0'
    })

@app.route('/test-favicon')
def test_favicon():
    """Test page for favicon functionality"""
    return render_template('test_favicon.html')

if __name__ == '__main__':
    # Set default user_id for development
    with app.test_request_context():
        with app.test_client() as client:
            with client.session_transaction() as sess:
                sess['user_id'] = 'default'
    
    # Run the app
    port = int(os.environ.get('PORT', 5001))
    debug = os.environ.get('FLASK_ENV') == 'development'
    
    if debug:
        app.run(host='0.0.0.0', port=port, debug=True)
    else:
        from waitress import serve
        logger.info(f"Starting Stock Watchlist Dashboard on port {port}")
        serve(app, host='0.0.0.0', port=port)