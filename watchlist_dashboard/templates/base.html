<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}Stock Watchlist Dashboard{% endblock %}</title>
    
    <!-- Favicon -->
    <link rel="icon" href="{{ url_for('static', filename='icons/chart.svg') }}" type="image/svg+xml">
    <link rel="icon" href="data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAzMiAzMiIgZmlsbD0ibm9uZSI+PGNpcmNsZSBjeD0iMTYiIGN5PSIxNiIgcj0iMTUiIGZpbGw9IiMzYjgyZjYiIHN0cm9rZT0iI2ZmZmZmZiIgc3Ryb2tlLXdpZHRoPSIyIi8+PHJlY3QgeD0iNiIgeT0iMjAiIHdpZHRoPSIzIiBoZWlnaHQ9IjYiIGZpbGw9IndoaXRlIiByeD0iMSIvPjxyZWN0IHg9IjExIiB5PSIxNiIgd2lkdGg9IjMiIGhlaWdodD0iMTAiIGZpbGw9IndoaXRlIiByeD0iMSIvPjxyZWN0IHg9IjE2IiB5PSIxMiIgd2lkdGg9IjMiIGhlaWdodD0iMTQiIGZpbGw9IndoaXRlIiByeD0iMSIvPjxyZWN0IHg9IjIxIiB5PSI4IiB3aWR0aD0iMyIgaGVpZ2h0PSIxOCIgZmlsbD0id2hpdGUiIHJ4PSIxIi8+PHBvbHlsaW5lIHBvaW50cz0iNy41LDIxIDEyLjUsMTcgMTcuNSwxMyAyMi41LDkiIHN0cm9rZT0id2hpdGUiIHN0cm9rZS13aWR0aD0iMiIgZmlsbD0ibm9uZSIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIi8+PGNpcmNsZSBjeD0iNy41IiBjeT0iMjEiIHI9IjEuNSIgZmlsbD0id2hpdGUiLz48Y2lyY2xlIGN4PSIxMi41IiBjeT0iMTciIHI9IjEuNSIgZmlsbD0id2hpdGUiLz48Y2lyY2xlIGN4PSIxNy41IiBjeT0iMTMiIHI9IjEuNSIgZmlsbD0id2hpdGUiLz48Y2lyY2xlIGN4PSIyMi41IiBjeT0iOSIgcj0iMS41IiBmaWxsPSJ3aGl0ZSIvPjwvc3ZnPg==" type="image/svg+xml">
    <link rel="apple-touch-icon" href="{{ url_for('static', filename='icons/chart.svg') }}">
    <link rel="manifest" href="{{ url_for('static', filename='manifest.json') }}">
    
    <!-- Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js@4.4.0/dist/chart.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chartjs-adapter-date-fns@3.0.0/dist/chartjs-adapter-date-fns.bundle.min.js"></script>
    
    <!-- D3.js for custom visualizations -->
    <script src="https://d3js.org/d3.v7.min.js"></script>
    
    <!-- Custom CSS -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/styles.css') }}">
    
    {% block head %}{% endblock %}
    
    <style>
        :root {
            /* Light theme colors */
            --bg-primary: #ffffff;
            --bg-secondary: #f8fafc;
            --bg-tertiary: #f1f5f9;
            --text-primary: #0f172a;
            --text-secondary: #475569;
            --text-muted: #94a3b8;
            --border-primary: #e2e8f0;
            --border-secondary: #cbd5e1;
            --accent-primary: #3b82f6;
            --accent-secondary: #6366f1;
            --success: #10b981;
            --warning: #f59e0b;
            --danger: #ef4444;
            --info: #06b6d4;
            
            /* Shadows */
            --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
            --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
            --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
            
            /* Spacing */
            --spacing-xs: 0.25rem;
            --spacing-sm: 0.5rem;
            --spacing-md: 1rem;
            --spacing-lg: 1.5rem;
            --spacing-xl: 2rem;
            --spacing-2xl: 3rem;
            
            /* Border radius */
            --radius-sm: 0.25rem;
            --radius-md: 0.5rem;
            --radius-lg: 0.75rem;
            --radius-xl: 1rem;
            
            /* Transitions */
            --transition-fast: 150ms ease-in-out;
            --transition-normal: 250ms ease-in-out;
            --transition-slow: 350ms ease-in-out;
        }
        
        /* Dark theme */
        [data-theme="dark"] {
            --bg-primary: #0f172a;
            --bg-secondary: #1e293b;
            --bg-tertiary: #334155;
            --text-primary: #f8fafc;
            --text-secondary: #cbd5e1;
            --text-muted: #64748b;
            --border-primary: #334155;
            --border-secondary: #475569;
        }
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
            background-color: var(--bg-secondary);
            color: var(--text-primary);
            line-height: 1.6;
            transition: background-color var(--transition-normal), color var(--transition-normal);
        }
        
        /* Navigation */
        .navbar {
            background-color: var(--bg-primary);
            border-bottom: 1px solid var(--border-primary);
            padding: var(--spacing-md) 0;
            position: sticky;
            top: 0;
            z-index: 100;
            backdrop-filter: blur(8px);
            transition: all var(--transition-normal);
        }
        
        .navbar-container {
            max-width: 1200px;
            margin: 0 auto;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 var(--spacing-lg);
        }
        
        .navbar-brand {
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--text-primary);
            text-decoration: none;
        }
        
        .navbar-brand .navbar-logo {
            width: 32px;
            height: 32px;
            margin-right: var(--spacing-sm);
            transition: transform var(--transition-fast);
            border-radius: var(--radius-sm);
            display: inline-block;
        }
        
        .navbar-brand:hover .navbar-logo {
            transform: scale(1.1);
        }
        
        .navbar-brand:hover {
            color: var(--accent-primary);
        }
        
        .navbar-nav {
            display: flex;
            align-items: center;
            gap: var(--spacing-lg);
            list-style: none;
        }
        
        .nav-link {
            color: var(--text-secondary);
            text-decoration: none;
            font-weight: 500;
            padding: var(--spacing-sm) var(--spacing-md);
            border-radius: var(--radius-md);
            transition: all var(--transition-fast);
        }
        
        .nav-link:hover {
            color: var(--accent-primary);
            background-color: var(--bg-tertiary);
        }
        
        .nav-link.active {
            color: var(--accent-primary);
            background-color: var(--bg-tertiary);
        }
        
        /* Theme toggle */
        .theme-toggle {
            background: none;
            border: none;
            color: var(--text-secondary);
            font-size: 1.2rem;
            padding: var(--spacing-sm);
            border-radius: var(--radius-md);
            cursor: pointer;
            transition: all var(--transition-fast);
        }
        
        .theme-toggle:hover {
            color: var(--accent-primary);
            background-color: var(--bg-tertiary);
        }
        
        /* Notification badge */
        .notification-badge {
            position: relative;
            cursor: pointer;
        }
        
        .notification-count {
            position: absolute;
            top: -8px;
            right: -8px;
            background-color: var(--danger);
            color: white;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            font-size: 0.75rem;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
        }
        
        /* Main content */
        .main-content {
            max-width: 1200px;
            margin: 0 auto;
            padding: var(--spacing-xl) var(--spacing-lg);
            min-height: calc(100vh - 80px);
        }
        
        /* Flash messages */
        .flash-messages {
            position: fixed;
            top: 80px;
            right: var(--spacing-lg);
            z-index: 1000;
            max-width: 400px;
        }
        
        .flash-message {
            background-color: var(--bg-primary);
            border: 1px solid var(--border-primary);
            border-radius: var(--radius-lg);
            padding: var(--spacing-md);
            margin-bottom: var(--spacing-sm);
            box-shadow: var(--shadow-lg);
            animation: slideIn 0.3s ease-out;
        }
        
        .flash-message.success {
            border-left: 4px solid var(--success);
            background-color: var(--success);
            color: white;
        }
        
        .flash-message.error {
            border-left: 4px solid var(--danger);
            background-color: var(--danger);
            color: white;
        }
        
        .flash-message.warning {
            border-left: 4px solid var(--warning);
            background-color: var(--warning);
            color: white;
        }
        
        .flash-message.info {
            border-left: 4px solid var(--info);
            background-color: var(--info);
            color: white;
        }
        
        @keyframes slideIn {
            from {
                transform: translateX(100%);
                opacity: 0;
            }
            to {
                transform: translateX(0);
                opacity: 1;
            }
        }
        
        /* Responsive design */
        @media (max-width: 768px) {
            .navbar-container {
                padding: 0 var(--spacing-md);
                flex-wrap: wrap;
            }
            
            .navbar-nav {
                gap: var(--spacing-md);
                margin-top: var(--spacing-sm);
                width: 100%;
                justify-content: center;
            }
            
            .main-content {
                padding: var(--spacing-lg) var(--spacing-md);
            }
            
            .flash-messages {
                right: var(--spacing-md);
                left: var(--spacing-md);
                max-width: none;
            }
        }
        
        /* Loading spinner */
        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 2px solid var(--text-muted);
            border-radius: 50%;
            border-top-color: var(--accent-primary);
            animation: spin 1s ease-in-out infinite;
        }
        
        @keyframes spin {
            to {
                transform: rotate(360deg);
            }
        }
        
        /* Utility classes */
        .text-center { text-align: center; }
        .text-left { text-align: left; }
        .text-right { text-align: right; }
        .text-primary { color: var(--text-primary); }
        .text-secondary { color: var(--text-secondary); }
        .text-muted { color: var(--text-muted); }
        .text-success { color: var(--success); }
        .text-warning { color: var(--warning); }
        .text-danger { color: var(--danger); }
        .text-info { color: var(--info); }
        
        .bg-primary { background-color: var(--bg-primary); }
        .bg-secondary { background-color: var(--bg-secondary); }
        .bg-tertiary { background-color: var(--bg-tertiary); }
        
        .border { border: 1px solid var(--border-primary); }
        .border-0 { border: none; }
        .rounded { border-radius: var(--radius-md); }
        .rounded-lg { border-radius: var(--radius-lg); }
        .rounded-xl { border-radius: var(--radius-xl); }
        
        .shadow-sm { box-shadow: var(--shadow-sm); }
        .shadow-md { box-shadow: var(--shadow-md); }
        .shadow-lg { box-shadow: var(--shadow-lg); }
        .shadow-xl { box-shadow: var(--shadow-xl); }
        
        .p-0 { padding: 0; }
        .p-1 { padding: var(--spacing-xs); }
        .p-2 { padding: var(--spacing-sm); }
        .p-3 { padding: var(--spacing-md); }
        .p-4 { padding: var(--spacing-lg); }
        .p-5 { padding: var(--spacing-xl); }
        .p-6 { padding: var(--spacing-2xl); }
        
        .m-0 { margin: 0; }
        .m-1 { margin: var(--spacing-xs); }
        .m-2 { margin: var(--spacing-sm); }
        .m-3 { margin: var(--spacing-md); }
        .m-4 { margin: var(--spacing-lg); }
        .m-5 { margin: var(--spacing-xl); }
        .m-6 { margin: var(--spacing-2xl); }
        
        .mb-1 { margin-bottom: var(--spacing-xs); }
        .mb-2 { margin-bottom: var(--spacing-sm); }
        .mb-3 { margin-bottom: var(--spacing-md); }
        .mb-4 { margin-bottom: var(--spacing-lg); }
        .mb-5 { margin-bottom: var(--spacing-xl); }
        .mb-6 { margin-bottom: var(--spacing-2xl); }
        
        .mt-1 { margin-top: var(--spacing-xs); }
        .mt-2 { margin-top: var(--spacing-sm); }
        .mt-3 { margin-top: var(--spacing-md); }
        .mt-4 { margin-top: var(--spacing-lg); }
        .mt-5 { margin-top: var(--spacing-xl); }
        .mt-6 { margin-top: var(--spacing-2xl); }
        
        .flex { display: flex; }
        .inline-flex { display: inline-flex; }
        .items-center { align-items: center; }
        .justify-center { justify-content: center; }
        .justify-between { justify-content: space-between; }
        .gap-1 { gap: var(--spacing-xs); }
        .gap-2 { gap: var(--spacing-sm); }
        .gap-3 { gap: var(--spacing-md); }
        .gap-4 { gap: var(--spacing-lg); }
        
        .hidden { display: none; }
        .block { display: block; }
        .inline-block { display: inline-block; }
        
        .cursor-pointer { cursor: pointer; }
        
        .transition { transition: all var(--transition-normal); }
        .transition-fast { transition: all var(--transition-fast); }
        .transition-slow { transition: all var(--transition-slow); }
    </style>
</head>
<body data-theme="light">
    <!-- Navigation -->
    <nav class="navbar">
        <div class="navbar-container">
            <a href="{{ url_for('index') }}" class="navbar-brand">
                <img src="{{ url_for('static', filename='icons/chart.svg') }}" alt="Logo" class="navbar-logo">
                Stock Watchlist
            </a>
            
            <ul class="navbar-nav">
                <li><a href="{{ url_for('index') }}" class="nav-link {% if request.endpoint == 'index' %}active{% endif %}">Dashboard</a></li>
                <li><a href="#" class="nav-link" onclick="showAddWatchModal()">Add Watch</a></li>
                <li class="notification-badge" onclick="toggleNotifications()">
                    <i class="fas fa-bell nav-link"></i>
                    <span class="notification-count" id="notificationCount" style="display: none;">0</span>
                </li>
                <li>
                    <button class="theme-toggle" onclick="toggleTheme()">
                        <i class="fas fa-moon" id="themeIcon"></i>
                    </button>
                </li>
            </ul>
        </div>
    </nav>
    
    <!-- Flash Messages -->
    <div class="flash-messages" id="flashMessages">
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                {% for category, message in messages %}
                    <div class="flash-message {{ category }}">
                        {{ message }}
                        <button onclick="this.parentElement.remove()" style="float: right; background: none; border: none; color: inherit; cursor: pointer;">×</button>
                    </div>
                {% endfor %}
            {% endif %}
        {% endwith %}
    </div>
    
    <!-- Main Content -->
    <div class="main-content">
        {% block content %}{% endblock %}
    </div>
    
    <!-- Notification Panel -->
    <div id="notificationPanel" class="hidden" style="position: fixed; top: 80px; right: 20px; width: 400px; max-height: 600px; background: var(--bg-primary); border: 1px solid var(--border-primary); border-radius: var(--radius-lg); box-shadow: var(--shadow-xl); z-index: 1000; overflow-y: auto;">
        <div class="p-4 border-bottom" style="border-bottom: 1px solid var(--border-primary);">
            <div class="flex justify-between items-center">
                <h3>Notifications</h3>
                <button onclick="markAllAsRead()" class="text-sm text-primary cursor-pointer">Mark all as read</button>
            </div>
        </div>
        <div id="notificationList" class="p-2">
            <div class="text-center text-muted p-4">Loading notifications...</div>
        </div>
    </div>
    
    <!-- Scripts -->
    <script src="{{ url_for('static', filename='js/main.js') }}"></script>
    
    <script>
        // Theme toggle functionality
        function toggleTheme() {
            const body = document.body;
            const themeIcon = document.getElementById('themeIcon');
            const currentTheme = body.getAttribute('data-theme');
            
            if (currentTheme === 'light') {
                body.setAttribute('data-theme', 'dark');
                themeIcon.className = 'fas fa-sun';
                localStorage.setItem('theme', 'dark');
            } else {
                body.setAttribute('data-theme', 'light');
                themeIcon.className = 'fas fa-moon';
                localStorage.setItem('theme', 'light');
            }
        }
        
        // Load saved theme
        document.addEventListener('DOMContentLoaded', function() {
            const savedTheme = localStorage.getItem('theme') || 'light';
            const body = document.body;
            const themeIcon = document.getElementById('themeIcon');
            
            body.setAttribute('data-theme', savedTheme);
            themeIcon.className = savedTheme === 'dark' ? 'fas fa-sun' : 'fas fa-moon';
            
            // Load notifications
            loadNotifications();
            
            // Set up notification polling
            setInterval(loadNotifications, 30000); // Check every 30 seconds
        });
        
        // Notification functionality
        let notificationPanelVisible = false;
        
        function toggleNotifications() {
            const panel = document.getElementById('notificationPanel');
            notificationPanelVisible = !notificationPanelVisible;
            
            if (notificationPanelVisible) {
                panel.classList.remove('hidden');
                loadNotifications();
            } else {
                panel.classList.add('hidden');
            }
        }
        
        async function loadNotifications() {
            try {
                const response = await fetch('/notifications');
                const notifications = await response.json();
                
                updateNotificationCount(notifications.filter(n => !n.is_read).length);
                displayNotifications(notifications);
            } catch (error) {
                console.error('Error loading notifications:', error);
            }
        }
        
        function updateNotificationCount(count) {
            const badge = document.getElementById('notificationCount');
            if (count > 0) {
                badge.textContent = count > 99 ? '99+' : count;
                badge.style.display = 'flex';
            } else {
                badge.style.display = 'none';
            }
        }
        
        function displayNotifications(notifications) {
            const list = document.getElementById('notificationList');
            
            if (notifications.length === 0) {
                list.innerHTML = '<div class="text-center text-muted p-4">No notifications</div>';
                return;
            }
            
            list.innerHTML = notifications.map(notification => `
                <div class="notification-item p-3 border-bottom ${notification.is_read ? 'text-muted' : ''}" style="border-bottom: 1px solid var(--border-primary);">
                    <div class="flex justify-between items-start gap-2">
                        <div class="flex-1">
                            <div class="font-medium text-sm">${notification.ticker} - ${notification.metric_name}</div>
                            <div class="text-sm mt-1">${notification.message}</div>
                            <div class="text-xs text-muted mt-1">${notification.time_ago}</div>
                        </div>
                        ${!notification.is_read ? `<button onclick="markAsRead(${notification.id})" class="text-xs text-primary cursor-pointer">Mark read</button>` : ''}
                    </div>
                </div>
            `).join('');
        }
        
        async function markAsRead(notificationId) {
            try {
                await fetch(`/notifications/${notificationId}/read`, { method: 'POST' });
                loadNotifications();
            } catch (error) {
                console.error('Error marking notification as read:', error);
            }
        }
        
        async function markAllAsRead() {
            try {
                const notifications = document.querySelectorAll('.notification-item button');
                for (const button of notifications) {
                    if (button.onclick) {
                        const notificationId = button.onclick.toString().match(/\d+/)[0];
                        await fetch(`/notifications/${notificationId}/read`, { method: 'POST' });
                    }
                }
                loadNotifications();
            } catch (error) {
                console.error('Error marking all notifications as read:', error);
            }
        }
        
        // Close notification panel when clicking outside
        document.addEventListener('click', function(event) {
            const panel = document.getElementById('notificationPanel');
            const badge = document.querySelector('.notification-badge');
            
            if (notificationPanelVisible && !panel.contains(event.target) && !badge.contains(event.target)) {
                panel.classList.add('hidden');
                notificationPanelVisible = false;
            }
        });
        
        // Auto-hide flash messages
        setTimeout(() => {
            const flashMessages = document.querySelectorAll('.flash-message');
            flashMessages.forEach(msg => {
                msg.style.animation = 'slideOut 0.3s ease-in forwards';
                setTimeout(() => msg.remove(), 300);
            });
        }, 5000);
    </script>
    
    {% block scripts %}{% endblock %}
</body>
</html>