{% extends "base.html" %}

{% block title %}Favicon Test - Stock Watchlist Dashboard{% endblock %}

{% block content %}
<div style="max-width: 800px; margin: 0 auto;">
    <h1>🚀 Stock Watchlist Dashboard - Favicon Test</h1>
    
    <p>This page tests the favicon functionality. You should see the chart icon in the browser tab!</p>
    
    <div class="card">
        <div class="card-header">
            <h2>Icon Preview</h2>
        </div>
        <div class="card-body text-center">
            <div style="display: inline-block; margin: 20px; text-align: center;">
                <img src="{{ url_for('static', filename='icons/chart.svg') }}" 
                     alt="Chart Icon" 
                     style="width: 64px; height: 64px; border: 1px solid var(--border-primary); border-radius: var(--radius-md); padding: 8px; background: white;">
                <div style="margin-top: 8px; font-weight: 500;">Chart SVG Icon</div>
            </div>
        </div>
    </div>
    
    <div class="card mt-4">
        <div class="card-header">
            <h2>Instructions</h2>
        </div>
        <div class="card-body">
            <ol>
                <li><strong>Check the browser tab</strong> - you should see a blue circular icon with chart bars</li>
                <li>If you don't see the icon, the SVG favicon might not be supported in your browser</li>
                <li>For full compatibility, generate a favicon.ico file (see instructions in <code>static/icons/README.md</code>)</li>
                <li>The favicon includes both SVG and base64 data URL fallback for maximum compatibility</li>
            </ol>
        </div>
    </div>
    
    <div class="card mt-4">
        <div class="card-header">
            <h2>Icon Details</h2>
        </div>
        <div class="card-body">
            <ul>
                <li><strong>Format:</strong> SVG (scalable vector graphics)</li>
                <li><strong>Size:</strong> 32x32 viewBox</li>
                <li><strong>Colors:</strong> Blue (#3b82f6) background with white chart elements</li>
                <li><strong>Design:</strong> Rising chart bars with trend line and data points</li>
                <li><strong>Fallback:</strong> Base64 encoded data URL for older browsers</li>
                <li><strong>PWA Ready:</strong> Includes manifest.json for installable app</li>
            </ul>
        </div>
    </div>
    
    <div class="card mt-4">
        <div class="card-header">
            <h2>Browser Compatibility</h2>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-6">
                    <h4 class="text-success">✅ Supported Browsers</h4>
                    <ul>
                        <li>Chrome 80+</li>
                        <li>Firefox 41+</li>
                        <li>Safari 9+</li>
                        <li>Edge 79+</li>
                    </ul>
                </div>
                <div class="col-md-6">
                    <h4 class="text-warning">⚠️ Limited Support</h4>
                    <ul>
                        <li>Internet Explorer (use .ico fallback)</li>
                        <li>Older mobile browsers</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
    
    <div class="mt-4 text-center">
        <a href="{{ url_for('index') }}" class="btn btn-primary">
            <i class="fas fa-home"></i> Back to Dashboard
        </a>
    </div>
</div>

<style>
.row {
    display: flex;
    gap: var(--spacing-lg);
}

.col-md-6 {
    flex: 1;
}

@media (max-width: 768px) {
    .row {
        flex-direction: column;
    }
}
</style>
{% endblock %}