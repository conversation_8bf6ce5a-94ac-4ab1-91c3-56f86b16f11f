{% extends "base.html" %}

{% block title %}Dashboard - Stock Watchlist{% endblock %}

{% block head %}
<style>
    /* Dashboard specific styles */
    .dashboard-header {
        background: linear-gradient(135deg, var(--accent-primary), var(--accent-secondary));
        color: white;
        padding: var(--spacing-2xl);
        border-radius: var(--radius-xl);
        margin-bottom: var(--spacing-xl);
        position: relative;
        overflow: hidden;
    }
    
    .dashboard-header::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
        opacity: 0.3;
    }
    
    .dashboard-header * {
        position: relative;
        z-index: 1;
    }
    
    .stats-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: var(--spacing-lg);
        margin-bottom: var(--spacing-xl);
    }
    
    .stat-card {
        background-color: var(--bg-primary);
        border: 1px solid var(--border-primary);
        border-radius: var(--radius-lg);
        padding: var(--spacing-lg);
        text-align: center;
        transition: all var(--transition-normal);
        position: relative;
        overflow: hidden;
    }
    
    .stat-card:hover {
        transform: translateY(-2px);
        box-shadow: var(--shadow-lg);
        border-color: var(--accent-primary);
    }
    
    .stat-card .icon {
        width: 3rem;
        height: 3rem;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto var(--spacing-md);
        font-size: 1.5rem;
        color: white;
    }
    
    .stat-card .icon.primary { background-color: var(--accent-primary); }
    .stat-card .icon.success { background-color: var(--success); }
    .stat-card .icon.warning { background-color: var(--warning); }
    .stat-card .icon.info { background-color: var(--info); }
    
    .stat-value {
        font-size: 2rem;
        font-weight: 700;
        color: var(--text-primary);
        margin-bottom: var(--spacing-xs);
    }
    
    .stat-label {
        color: var(--text-secondary);
        font-weight: 500;
    }
    
    .watchlist-grid {
        display: grid;
        gap: var(--spacing-lg);
    }
    
    .watchlist-card {
        background-color: var(--bg-primary);
        border: 1px solid var(--border-primary);
        border-radius: var(--radius-lg);
        padding: var(--spacing-lg);
        transition: all var(--transition-normal);
    }
    
    .watchlist-card:hover {
        box-shadow: var(--shadow-md);
        border-color: var(--accent-primary);
    }
    
    .watchlist-header {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        margin-bottom: var(--spacing-lg);
        padding-bottom: var(--spacing-md);
        border-bottom: 1px solid var(--border-primary);
    }
    
    .company-info h3 {
        margin: 0 0 var(--spacing-xs);
        font-size: 1.25rem;
        font-weight: 600;
        color: var(--text-primary);
    }
    
    .company-info .ticker {
        color: var(--text-muted);
        font-size: 0.875rem;
        font-weight: 500;
    }
    
    .metrics-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: var(--spacing-md);
        margin-bottom: var(--spacing-lg);
    }
    
    .metric-card {
        background-color: var(--bg-secondary);
        border-radius: var(--radius-md);
        padding: var(--spacing-md);
        transition: all var(--transition-fast);
    }
    
    .metric-card:hover {
        background-color: var(--bg-tertiary);
    }
    
    .metric-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: var(--spacing-sm);
    }
    
    .metric-name {
        font-weight: 600;
        color: var(--text-primary);
        font-size: 0.875rem;
    }
    
    .metric-type {
        font-size: 0.75rem;
        padding: 0.125rem 0.5rem;
        border-radius: var(--radius-sm);
        background-color: var(--accent-primary);
        color: white;
    }
    
    .metric-value {
        font-size: 1.5rem;
        font-weight: 700;
        margin-bottom: var(--spacing-xs);
    }
    
    .metric-condition {
        font-size: 0.75rem;
        color: var(--text-muted);
    }
    
    .add-watch-btn {
        position: fixed;
        bottom: 2rem;
        right: 2rem;
        width: 60px;
        height: 60px;
        border-radius: 50%;
        background-color: var(--accent-primary);
        color: white;
        border: none;
        font-size: 1.5rem;
        cursor: pointer;
        box-shadow: var(--shadow-lg);
        transition: all var(--transition-normal);
        z-index: 50;
    }
    
    .add-watch-btn:hover {
        background-color: var(--accent-secondary);
        transform: scale(1.1);
    }
    
    .empty-state {
        text-align: center;
        padding: var(--spacing-2xl);
        color: var(--text-muted);
    }
    
    .empty-state i {
        font-size: 4rem;
        margin-bottom: var(--spacing-lg);
        opacity: 0.5;
    }
    
    .empty-state h3 {
        margin-bottom: var(--spacing-md);
        color: var(--text-secondary);
    }
    
    /* Loading skeleton */
    .skeleton {
        background: linear-gradient(90deg, var(--bg-secondary) 25%, var(--bg-tertiary) 50%, var(--bg-secondary) 75%);
        background-size: 200% 100%;
        animation: loading 1.5s infinite;
    }
    
    @keyframes loading {
        0% { background-position: 200% 0; }
        100% { background-position: -200% 0; }
    }
    
    .skeleton-card {
        height: 200px;
        border-radius: var(--radius-lg);
        margin-bottom: var(--spacing-lg);
    }
    
    .hidden {
        display: none !important;
    }
    
    /* Modal Styles */
    .modal-overlay {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(0, 0, 0, 0.7);
        backdrop-filter: blur(8px);
        z-index: 1000;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: var(--spacing-lg);
    }
    
    .modal-container {
        background: var(--bg-primary);
        border-radius: var(--radius-xl);
        box-shadow: var(--shadow-xl);
        width: 100%;
        max-width: 900px;
        max-height: 90vh;
        opacity: 0;
        transform: translateY(-20px) scale(0.95);
        transition: all 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
        overflow: hidden;
        display: flex;
        flex-direction: column;
    }
    
    .modal-header {
        padding: var(--spacing-xl) var(--spacing-xl) var(--spacing-lg);
        border-bottom: 1px solid var(--border-primary);
        display: flex;
        justify-content: space-between;
        align-items: center;
        background: linear-gradient(135deg, var(--accent-primary), var(--accent-secondary));
        color: white;
        position: relative;
        overflow: hidden;
    }
    
    .modal-header::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: url('data:image/svg+xml,<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 100 100\"><defs><pattern id=\"dots\" width=\"10\" height=\"10\" patternUnits=\"userSpaceOnUse\"><circle cx=\"5\" cy=\"5\" r=\"1\" fill=\"rgba(255,255,255,0.1)\"/></pattern></defs><rect width=\"100\" height=\"100\" fill=\"url(%23dots)\"/></svg>');
        opacity: 0.3;
    }
    
    .modal-header * {
        position: relative;
        z-index: 1;
    }
    
    .modal-title {
        display: flex;
        align-items: center;
        gap: var(--spacing-md);
        font-size: 1.5rem;
        font-weight: 700;
    }
    
    .modal-title i {
        font-size: 1.75rem;
        animation: pulse 2s infinite;
    }
    
    .modal-close {
        background: rgba(255, 255, 255, 0.2);
        border: none;
        border-radius: 50%;
        width: 40px;
        height: 40px;
        color: white;
        cursor: pointer;
        font-size: 1.2rem;
        transition: all var(--transition-fast);
        backdrop-filter: blur(10px);
    }
    
    .modal-close:hover {
        background: rgba(255, 255, 255, 0.3);
        transform: scale(1.1);
    }
    
    .modal-body {
        flex: 1;
        overflow-y: auto;
        padding: var(--spacing-xl);
    }
    
    .modal-footer {
        padding: var(--spacing-lg) var(--spacing-xl);
        border-top: 1px solid var(--border-primary);
        display: flex;
        gap: var(--spacing-md);
        align-items: center;
        background: var(--bg-secondary);
    }
    
    .btn-spacer {
        flex: 1;
    }
    
    .btn {
        padding: var(--spacing-md) var(--spacing-lg);
        border-radius: var(--radius-md);
        font-weight: 600;
        cursor: pointer;
        border: none;
        transition: all var(--transition-fast);
        display: inline-flex;
        align-items: center;
        gap: var(--spacing-sm);
        font-size: 0.9rem;
        position: relative;
        overflow: hidden;
    }
    
    .btn::before {
        content: '';
        position: absolute;
        top: 50%;
        left: 50%;
        width: 0;
        height: 0;
        background: rgba(255, 255, 255, 0.2);
        border-radius: 50%;
        transform: translate(-50%, -50%);
        transition: width 0.3s, height 0.3s;
    }
    
    .btn:hover::before {
        width: 300px;
        height: 300px;
    }
    
    .btn-primary {
        background: var(--accent-primary);
        color: white;
    }
    
    .btn-primary:hover {
        background: var(--accent-secondary);
        transform: translateY(-2px);
        box-shadow: var(--shadow-lg);
    }
    
    .btn-secondary {
        background: var(--bg-tertiary);
        color: var(--text-secondary);
    }
    
    .btn-secondary:hover {
        background: var(--border-secondary);
        color: var(--text-primary);
    }
    
    .btn-success {
        background: var(--success);
        color: white;
    }
    
    .btn-success:hover {
        background: #059669;
        transform: translateY(-2px);
        box-shadow: var(--shadow-lg);
    }
    
    .btn:disabled {
        opacity: 0.6;
        cursor: not-allowed;
        transform: none !important;
    }
    
    /* Step Indicator */
    .step-indicator {
        display: flex;
        align-items: center;
        justify-content: center;
        margin-bottom: var(--spacing-2xl);
        padding: var(--spacing-lg);
        background: var(--bg-secondary);
        border-radius: var(--radius-lg);
    }
    
    .step {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: var(--spacing-sm);
        transition: all var(--transition-normal);
    }
    
    .step-number {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        background: var(--bg-tertiary);
        color: var(--text-muted);
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: 700;
        transition: all var(--transition-normal);
        border: 2px solid var(--border-primary);
    }
    
    .step.active .step-number {
        background: var(--accent-primary);
        color: white;
        border-color: var(--accent-primary);
        box-shadow: 0 0 20px rgba(59, 130, 246, 0.3);
        animation: pulse 2s infinite;
    }
    
    .step-label {
        font-size: 0.875rem;
        font-weight: 600;
        color: var(--text-muted);
        text-align: center;
        transition: all var(--transition-normal);
    }
    
    .step.active .step-label {
        color: var(--accent-primary);
    }
    
    .step-line {
        width: 60px;
        height: 2px;
        background: var(--border-primary);
        margin: 0 var(--spacing-lg);
    }
    
    /* Search Section */
    .search-section {
        text-align: center;
    }
    
    .search-header h3 {
        margin-bottom: var(--spacing-sm);
        color: var(--text-primary);
        display: flex;
        align-items: center;
        justify-content: center;
        gap: var(--spacing-md);
    }
    
    .search-header h3 i {
        color: var(--accent-primary);
        animation: bounce 2s infinite;
    }
    
    .search-header p {
        color: var(--text-secondary);
        margin-bottom: var(--spacing-xl);
    }
    
    .search-container {
        max-width: 500px;
        margin: 0 auto var(--spacing-xl);
    }
    
    .search-input-wrapper {
        position: relative;
        margin-bottom: var(--spacing-lg);
    }
    
    .search-icon {
        position: absolute;
        left: var(--spacing-md);
        top: 50%;
        transform: translateY(-50%);
        color: var(--text-muted);
        z-index: 1;
    }
    
    .search-loading {
        position: absolute;
        right: var(--spacing-md);
        top: 50%;
        transform: translateY(-50%);
        color: var(--accent-primary);
    }
    
    #stockSearch {
        width: 100%;
        padding: var(--spacing-md) var(--spacing-2xl) var(--spacing-md) 50px;
        border: 2px solid var(--border-primary);
        border-radius: var(--radius-lg);
        font-size: 1rem;
        background: var(--bg-primary);
        color: var(--text-primary);
        transition: all var(--transition-fast);
    }
    
    #stockSearch:focus {
        outline: none;
        border-color: var(--accent-primary);
        box-shadow: 0 0 20px rgba(59, 130, 246, 0.2);
    }
    
    .search-results {
        background: var(--bg-primary);
        border: 1px solid var(--border-primary);
        border-radius: var(--radius-lg);
        max-height: 300px;
        overflow-y: auto;
        box-shadow: var(--shadow-lg);
    }
    
    .search-result {
        padding: var(--spacing-md) var(--spacing-lg);
        display: flex;
        justify-content: space-between;
        align-items: center;
        cursor: pointer;
        transition: all var(--transition-fast);
        border-bottom: 1px solid var(--border-primary);
    }
    
    .search-result:last-child {
        border-bottom: none;
    }
    
    .search-result:hover {
        background: var(--bg-secondary);
        transform: translateX(5px);
    }
    
    .stock-ticker {
        font-weight: 700;
        color: var(--accent-primary);
        font-size: 1.1rem;
    }
    
    .stock-name {
        font-weight: 600;
        color: var(--text-primary);
        margin-bottom: 2px;
    }
    
    .stock-sector {
        font-size: 0.875rem;
        color: var(--text-muted);
    }
    
    .no-results, .search-error {
        padding: var(--spacing-xl);
        text-align: center;
        color: var(--text-muted);
        font-style: italic;
    }
    
    /* Popular Stocks */
    .popular-stocks h4 {
        color: var(--text-primary);
        margin-bottom: var(--spacing-lg);
        text-align: left;
    }
    
    .popular-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: var(--spacing-md);
    }
    
    .popular-stock {
        background: var(--bg-secondary);
        padding: var(--spacing-md);
        border-radius: var(--radius-md);
        display: flex;
        align-items: center;
        gap: var(--spacing-md);
        cursor: pointer;
        transition: all var(--transition-fast);
        border: 2px solid transparent;
    }
    
    .popular-stock:hover {
        background: var(--bg-tertiary);
        border-color: var(--accent-primary);
        transform: translateY(-2px);
        box-shadow: var(--shadow-md);
    }
    
    .popular-stock i {
        font-size: 1.5rem;
        color: var(--accent-primary);
        width: 30px;
        text-align: center;
    }
    
    .popular-stock .ticker {
        font-weight: 700;
        color: var(--text-primary);
    }
    
    .popular-stock .name {
        font-size: 0.875rem;
        color: var(--text-secondary);
    }
    
    /* Selected Stock Info */
    .selected-stock {
        display: flex;
        align-items: center;
        gap: var(--spacing-lg);
        background: linear-gradient(135deg, var(--success), #059669);
        color: white;
        padding: var(--spacing-lg);
        border-radius: var(--radius-lg);
        margin-bottom: var(--spacing-xl);
        position: relative;
        overflow: hidden;
    }
    
    .selected-stock::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: url('data:image/svg+xml,<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 100 100\"><defs><pattern id=\"check\" width=\"20\" height=\"20\" patternUnits=\"userSpaceOnUse\"><path d=\"M5 10l3 3 7-7\" stroke=\"rgba(255,255,255,0.1)\" stroke-width=\"2\" fill=\"none\"/></pattern></defs><rect width=\"100\" height=\"100\" fill=\"url(%23check)\"/></svg>');
        opacity: 0.3;
    }
    
    .selected-stock * {
        position: relative;
        z-index: 1;
    }
    
    .stock-avatar {
        width: 50px;
        height: 50px;
        background: rgba(255, 255, 255, 0.2);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.5rem;
    }
    
    .stock-details {
        flex: 1;
    }
    
    .selected-stock .stock-ticker {
        font-size: 1.25rem;
        font-weight: 700;
        margin-bottom: 2px;
    }
    
    .selected-stock .stock-name {
        opacity: 0.9;
    }
    
    .stock-status {
        display: flex;
        align-items: center;
        gap: var(--spacing-sm);
        font-weight: 600;
    }
    
    .stock-status i {
        font-size: 1.25rem;
        animation: checkmark 0.5s ease-in-out;
    }
    
    /* Metrics Section */
    .metrics-categories {
        display: flex;
        justify-content: center;
        gap: var(--spacing-md);
        margin-bottom: var(--spacing-xl);
    }
    
    .metric-category {
        background: var(--bg-secondary);
        padding: var(--spacing-md) var(--spacing-lg);
        border-radius: var(--radius-lg);
        display: flex;
        align-items: center;
        gap: var(--spacing-sm);
        cursor: pointer;
        transition: all var(--transition-fast);
        border: 2px solid transparent;
        font-weight: 600;
        color: var(--text-secondary);
    }
    
    .metric-category:hover {
        background: var(--bg-tertiary);
        border-color: var(--accent-primary);
        color: var(--text-primary);
    }
    
    .metric-category.active {
        background: var(--accent-primary);
        color: white;
        border-color: var(--accent-primary);
        box-shadow: 0 0 20px rgba(59, 130, 246, 0.3);
    }
    
    .metric-category i {
        font-size: 1.1rem;
    }
    
    .metrics-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
        gap: var(--spacing-md);
    }
    
    .metric-option {
        background: var(--bg-secondary);
        border: 2px solid var(--border-primary);
        border-radius: var(--radius-lg);
        padding: var(--spacing-lg);
        cursor: pointer;
        transition: all var(--transition-fast);
        position: relative;
        overflow: hidden;
    }
    
    .metric-option:hover {
        border-color: var(--accent-primary);
        box-shadow: var(--shadow-md);
        transform: translateY(-2px);
    }
    
    .metric-option.selected {
        border-color: var(--success);
        background: linear-gradient(135deg, rgba(16, 185, 129, 0.1), rgba(5, 150, 105, 0.1));
        box-shadow: var(--shadow-lg);
    }
    
    .metric-option .metric-icon {
        width: 50px;
        height: 50px;
        background: var(--accent-primary);
        color: white;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.5rem;
        margin-bottom: var(--spacing-md);
        transition: all var(--transition-fast);
    }
    
    .metric-option.selected .metric-icon {
        background: var(--success);
        animation: bounce 0.5s ease-in-out;
    }
    
    .metric-name {
        font-weight: 700;
        color: var(--text-primary);
        margin-bottom: var(--spacing-xs);
        font-size: 1.1rem;
    }
    
    .metric-description {
        color: var(--text-secondary);
        font-size: 0.9rem;
        line-height: 1.4;
    }
    
    .metric-checkbox {
        position: absolute;
        top: var(--spacing-md);
        right: var(--spacing-md);
        width: 25px;
        height: 25px;
        background: var(--success);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        opacity: 0;
        transform: scale(0);
        transition: all var(--transition-fast);
    }
    
    .metric-option.selected .metric-checkbox {
        opacity: 1;
        transform: scale(1);
        animation: checkmark 0.3s ease-in-out;
    }
    
    /* Alerts Section */
    .alert-config {
        background: var(--bg-secondary);
        border-radius: var(--radius-lg);
        padding: var(--spacing-lg);
        margin-bottom: var(--spacing-md);
        border: 1px solid var(--border-primary);
    }
    
    .alert-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: var(--spacing-md);
    }
    
    .alert-header h4 {
        color: var(--text-primary);
        font-weight: 600;
    }
    
    .alert-toggle {
        position: relative;
        display: inline-block;
        width: 60px;
        height: 34px;
    }
    
    .alert-toggle input {
        opacity: 0;
        width: 0;
        height: 0;
    }
    
    .slider {
        position: absolute;
        cursor: pointer;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-color: var(--border-secondary);
        transition: .4s;
        border-radius: 34px;
    }
    
    .slider:before {
        position: absolute;
        content: \"\";
        height: 26px;
        width: 26px;
        left: 4px;
        bottom: 4px;
        background-color: white;
        transition: .4s;
        border-radius: 50%;
        box-shadow: 0 2px 4px rgba(0,0,0,0.2);
    }
    
    input:checked + .slider {
        background-color: var(--success);
    }
    
    input:checked + .slider:before {
        transform: translateX(26px);
    }
    
    .alert-settings {
        padding-top: var(--spacing-md);
        border-top: 1px solid var(--border-primary);
    }
    
    .condition-row {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: var(--spacing-md);
        margin-bottom: var(--spacing-md);
    }
    
    .condition-type, .target-value {
        padding: var(--spacing-md);
        border: 1px solid var(--border-primary);
        border-radius: var(--radius-md);
        background: var(--bg-primary);
        color: var(--text-primary);
        font-size: 0.9rem;
        transition: all var(--transition-fast);
    }
    
    .condition-type:focus, .target-value:focus {
        outline: none;
        border-color: var(--accent-primary);
        box-shadow: 0 0 10px rgba(59, 130, 246, 0.2);
    }
    
    .notification-options {
        display: flex;
        gap: var(--spacing-lg);
    }
    
    .notification-options label {
        display: flex;
        align-items: center;
        gap: var(--spacing-sm);
        font-size: 0.9rem;
        color: var(--text-secondary);
        cursor: pointer;
    }
    
    .forecast-section h4 {
        color: var(--text-primary);
        margin-bottom: var(--spacing-lg);
        display: flex;
        align-items: center;
        gap: var(--spacing-md);
    }
    
    .forecast-section h4 i {
        color: var(--accent-secondary);
        animation: glow 2s infinite alternate;
    }
    
    .forecast-option {
        background: var(--bg-secondary);
        border: 2px solid var(--border-primary);
        border-radius: var(--radius-lg);
        padding: var(--spacing-lg);
        display: flex;
        align-items: center;
        gap: var(--spacing-md);
        cursor: pointer;
        transition: all var(--transition-fast);
    }
    
    .forecast-option:hover {
        border-color: var(--accent-primary);
        background: var(--bg-tertiary);
    }
    
    .forecast-option input:checked + .checkmark {
        background: var(--accent-primary);
    }
    
    .checkmark {
        width: 20px;
        height: 20px;
        background: var(--bg-primary);
        border: 2px solid var(--border-primary);
        border-radius: 4px;
        transition: all var(--transition-fast);
    }
    
    .forecast-title {
        font-weight: 600;
        color: var(--text-primary);
        margin-bottom: 2px;
    }
    
    .forecast-description {
        font-size: 0.875rem;
        color: var(--text-secondary);
        line-height: 1.4;
    }
    
    /* Animations */
    @keyframes pulse {
        0%, 100% { opacity: 1; }
        50% { opacity: 0.7; }
    }
    
    @keyframes bounce {
        0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
        40% { transform: translateY(-10px); }
        60% { transform: translateY(-5px); }
    }
    
    @keyframes checkmark {
        0% { transform: scale(0) rotate(0deg); }
        50% { transform: scale(1.2) rotate(180deg); }
        100% { transform: scale(1) rotate(360deg); }
    }
    
    @keyframes glow {
        from { text-shadow: 0 0 5px var(--accent-secondary); }
        to { text-shadow: 0 0 15px var(--accent-secondary), 0 0 25px var(--accent-secondary); }
    }
    
    /* Responsive design */
    @media (max-width: 768px) {
        .dashboard-header {
            padding: var(--spacing-lg);
        }
        
        .stats-grid {
            grid-template-columns: repeat(2, 1fr);
        }
        
        .metrics-grid {
            grid-template-columns: 1fr;
        }
        
        .add-watch-btn {
            bottom: 1rem;
            right: 1rem;
            width: 50px;
            height: 50px;
            font-size: 1.25rem;
        }
        
        .modal-overlay {
            padding: var(--spacing-md);
        }
        
        .modal-container {
            max-height: 95vh;
        }
        
        .modal-header, .modal-body, .modal-footer {
            padding: var(--spacing-lg);
        }
        
        .step-indicator {
            flex-direction: column;
            gap: var(--spacing-md);
        }
        
        .step-line {
            width: 2px;
            height: 30px;
            margin: 0;
        }
        
        .metrics-categories {
            flex-direction: column;
            align-items: center;
        }
        
        .popular-grid {
            grid-template-columns: 1fr;
        }
        
        .modal-footer {
            flex-direction: column;
            gap: var(--spacing-sm);
        }
        
        .btn-spacer {
            display: none;
        }
    }
</style>
{% endblock %}

{% block content %}
<!-- Dashboard Header -->
<div class="dashboard-header">
    <div class="flex justify-between items-center">
        <div>
            <h1 style="font-size: 2.5rem; font-weight: 700; margin-bottom: var(--spacing-sm);">
                Stock Watchlist Dashboard
            </h1>
            <p style="font-size: 1.125rem; opacity: 0.9;">
                Track your investments, set alerts, and forecast future performance
            </p>
        </div>
        <div style="text-align: center;">
            <div style="font-size: 2rem; font-weight: 700;">{{ watchlists|length }}</div>
            <div style="opacity: 0.8;">Stocks Tracked</div>
        </div>
    </div>
</div>

<!-- Stats Grid -->
<div class="stats-grid">
    <div class="stat-card">
        <div class="icon primary">
            <i class="fas fa-chart-line"></i>
        </div>
        <div class="stat-value">{{ total_stocks }}</div>
        <div class="stat-label">Stocks Tracked</div>
    </div>
    
    <div class="stat-card">
        <div class="icon success">
            <i class="fas fa-bell"></i>
        </div>
        <div class="stat-value">{{ active_alerts }}</div>
        <div class="stat-label">Active Alerts</div>
    </div>
    
    <div class="stat-card">
        <div class="icon warning">
            <i class="fas fa-target"></i>
        </div>
        <div class="stat-value" id="forecastCount">0</div>
        <div class="stat-label">Active Forecasts</div>
    </div>
    
    <div class="stat-card">
        <div class="icon info">
            <i class="fas fa-chart-bar"></i>
        </div>
        <div class="stat-value" id="metricsCount">0</div>
        <div class="stat-label">Total Metrics</div>
    </div>
</div>

<!-- Watchlist Grid -->
<div class="watchlist-grid" id="watchlistGrid">
    {% if watchlists %}
        {% for watchlist in watchlists %}
        <div class="watchlist-card" data-ticker="{{ watchlist.ticker }}">
            <div class="watchlist-header">
                <div class="company-info">
                    <h3>{{ watchlist.company_name or watchlist.ticker }}</h3>
                    <div class="ticker">{{ watchlist.ticker }}</div>
                </div>
                <div class="flex gap-2">
                    <button onclick="refreshWatchlist('{{ watchlist.ticker }}')" 
                            class="text-sm" style="background: none; border: none; color: var(--text-muted); cursor: pointer;">
                        <i class="fas fa-sync-alt"></i>
                    </button>
                    <button onclick="removeWatchlist('{{ watchlist.ticker }}')" 
                            class="text-sm" style="background: none; border: none; color: var(--danger); cursor: pointer;">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </div>
            
            <div class="metrics-grid">
                {% for metric in watchlist.metrics %}
                <div class="metric-card" data-metric="{{ metric.metric_name }}">
                    <div class="metric-header">
                        <div class="metric-name">{{ metric.metric_name|replace('_', ' ')|title }}</div>
                        <div class="metric-type">{{ metric.metric_type }}</div>
                    </div>
                    <div class="metric-value loading" id="value-{{ watchlist.ticker }}-{{ metric.metric_name }}">
                        <div class="loading">Loading...</div>
                    </div>
                    {% if metric.condition_type and metric.target_value %}
                    <div class="metric-condition">
                        Alert: {{ metric.condition_type|replace('_', ' ') }} {{ metric.target_value }}
                    </div>
                    {% endif %}
                    {% if metric.description %}
                    <div class="metric-condition">{{ metric.description }}</div>
                    {% endif %}
                </div>
                {% endfor %}
            </div>
            
            {% if watchlist.forecasts %}
            <div style="margin-top: var(--spacing-lg); padding-top: var(--spacing-md); border-top: 1px solid var(--border-primary);">
                <h4 style="margin-bottom: var(--spacing-sm); color: var(--text-primary);">
                    Active Forecasts ({{ watchlist.forecasts|length }})
                </h4>
                <div style="display: flex; flex-wrap: wrap; gap: var(--spacing-sm);">
                    {% for forecast in watchlist.forecasts %}
                    <div style="background-color: var(--bg-secondary); padding: var(--spacing-sm); border-radius: var(--radius-sm); font-size: 0.75rem;">
                        {{ forecast.metric_name|replace('_', ' ')|title }}: {{ forecast.forecast_value|currency }}
                    </div>
                    {% endfor %}
                </div>
            </div>
            {% endif %}
        </div>
        {% endfor %}
    {% else %}
        <!-- Empty State -->
        <div class="empty-state">
            <i class="fas fa-chart-line"></i>
            <h3>No stocks in your watchlist yet</h3>
            <p>Start tracking stocks by adding them to your watchlist. Set custom alerts and forecasts to stay on top of your investments.</p>
            <button onclick="showAddWatchModal()" 
                    style="background-color: var(--accent-primary); color: white; border: none; padding: var(--spacing-md) var(--spacing-lg); border-radius: var(--radius-md); font-weight: 600; cursor: pointer; margin-top: var(--spacing-lg); transition: all var(--transition-fast);"
                    onmouseover="this.style.backgroundColor='var(--accent-secondary)'"
                    onmouseout="this.style.backgroundColor='var(--accent-primary)'">
                <i class="fas fa-plus"></i> Add Your First Stock
            </button>
        </div>
    {% endif %}
</div>

<!-- Loading Skeletons (initially hidden) -->
<div id="loadingSkeletons" class="hidden">
    <div class="skeleton skeleton-card"></div>
    <div class="skeleton skeleton-card"></div>
    <div class="skeleton skeleton-card"></div>
</div>

<!-- Add Watch Button -->
<button class="add-watch-btn" onclick="showAddWatchModal()" title="Add new stock to watchlist">
    <i class="fas fa-plus"></i>
</button>

<!-- Add Watch Modal (will be created by JavaScript) -->
<div id="addWatchModal" class="hidden" style="display: none;"></div>
{% endblock %}

{% block scripts %}
<script>
    // Dashboard functionality
    let watchlistData = {{ watchlists|tojson|safe }};
    let refreshInterval;
    
    document.addEventListener('DOMContentLoaded', function() {
        initializeDashboard();
        startAutoRefresh();
        updateSummaryStats();
    });
    
    function initializeDashboard() {
        // Load current data for all metrics
        watchlistData.forEach(watchlist => {
            watchlist.metrics.forEach(metric => {
                loadMetricData(watchlist.ticker, metric.metric_name);
            });
        });
    }
    
    function startAutoRefresh() {
        // Refresh data every 30 seconds
        refreshInterval = setInterval(() => {
            watchlistData.forEach(watchlist => {
                watchlist.metrics.forEach(metric => {
                    loadMetricData(watchlist.ticker, metric.metric_name);
                });
            });
        }, 30000);
    }
    
    async function loadMetricData(ticker, metric) {
        try {
            const response = await fetch(`/data/${ticker}/${metric}`);
            const data = await response.json();
            
            if (data.current) {
                updateMetricDisplay(ticker, metric, data.current);
            }
        } catch (error) {
            console.error(`Error loading data for ${ticker}/${metric}:`, error);
            updateMetricDisplay(ticker, metric, { value: 'Error', error: true });
        }
    }
    
    function updateMetricDisplay(ticker, metric, data) {
        const element = document.getElementById(`value-${ticker}-${metric}`);
        if (!element) return;
        
        if (data.error) {
            element.innerHTML = '<span class="text-danger">Error</span>';
            element.classList.remove('loading');
            return;
        }
        
        // Format value based on metric type
        let formattedValue = formatMetricValue(data.value, metric);
        let changeClass = '';
        
        // Add change indicator for price metrics
        if (data.previous_close && metric === 'current_price') {
            const change = data.value - data.previous_close;
            const changePercent = (change / data.previous_close) * 100;
            changeClass = change >= 0 ? 'text-success' : 'text-danger';
            formattedValue += ` <small class="${changeClass}">(${change >= 0 ? '+' : ''}${changePercent.toFixed(2)}%)</small>`;
        } else if (data.absolute_change && metric === 'day_change') {
            changeClass = data.value >= 0 ? 'text-success' : 'text-danger';
        }
        
        element.innerHTML = `<span class="${changeClass}">${formattedValue}</span>`;
        element.classList.remove('loading');
        
        // Add sparkle animation for significant changes
        if (Math.abs(data.value) > 5 && (metric === 'day_change' || metric === 'current_price')) {
            element.style.animation = 'sparkle 0.5s ease-in-out';
            setTimeout(() => element.style.animation = '', 500);
        }
    }
    
    function formatMetricValue(value, metric) {
        if (value === null || value === undefined) return 'N/A';
        
        // Currency metrics
        if (['current_price', 'book_value', 'market_cap', 'total_revenue', 'net_income', 'free_cash_flow'].includes(metric)) {
            return formatCurrency(value);
        }
        // Percentage metrics
        else if (['day_change', 'rsi'].includes(metric)) {
            return value.toFixed(2) + '%';
        }
        // Ratio metrics
        else if (['pe_ratio', 'price_to_book'].includes(metric)) {
            return value.toFixed(2);
        }
        // Large number metrics
        else if (['volume', 'total_assets', 'total_debt'].includes(metric)) {
            return formatLargeNumber(value);
        }
        else {
            return value.toFixed(2);
        }
    }
    
    function formatCurrency(value) {
        const absValue = Math.abs(value);
        if (absValue >= 1e12) return `$${(value / 1e12).toFixed(2)}T`;
        if (absValue >= 1e9) return `$${(value / 1e9).toFixed(2)}B`;
        if (absValue >= 1e6) return `$${(value / 1e6).toFixed(2)}M`;
        if (absValue >= 1e3) return `$${(value / 1e3).toFixed(2)}K`;
        return `$${value.toFixed(2)}`;
    }
    
    function formatLargeNumber(value) {
        const absValue = Math.abs(value);
        if (absValue >= 1e12) return `${(value / 1e12).toFixed(2)}T`;
        if (absValue >= 1e9) return `${(value / 1e9).toFixed(2)}B`;
        if (absValue >= 1e6) return `${(value / 1e6).toFixed(2)}M`;
        if (absValue >= 1e3) return `${(value / 1e3).toFixed(2)}K`;
        return value.toFixed(0);
    }
    
    function updateSummaryStats() {
        let totalMetrics = 0;
        let totalForecasts = 0;
        
        watchlistData.forEach(watchlist => {
            totalMetrics += watchlist.metrics.length;
            totalForecasts += watchlist.forecasts.length;
        });
        
        document.getElementById('metricsCount').textContent = totalMetrics;
        document.getElementById('forecastCount').textContent = totalForecasts;
    }
    
    async function refreshWatchlist(ticker) {
        const watchlist = watchlistData.find(w => w.ticker === ticker);
        if (!watchlist) return;
        
        // Show loading state
        watchlist.metrics.forEach(metric => {
            const element = document.getElementById(`value-${ticker}-${metric.metric_name}`);
            if (element) {
                element.innerHTML = '<div class="loading">Loading...</div>';
                element.classList.add('loading');
            }
        });
        
        // Reload all metrics for this ticker
        for (const metric of watchlist.metrics) {
            await loadMetricData(ticker, metric.metric_name);
        }
    }
    
    async function removeWatchlist(ticker) {
        if (!confirm(`Are you sure you want to remove ${ticker} from your watchlist?`)) {
            return;
        }
        
        try {
            // Find all watchlist items for this ticker
            const watchlist = watchlistData.find(w => w.ticker === ticker);
            if (!watchlist) return;
            
            // Remove all metrics for this ticker
            for (const metric of watchlist.metrics) {
                const response = await fetch(`/watchlist/${metric.id}`, {
                    method: 'DELETE'
                });
                if (!response.ok) {
                    throw new Error('Failed to remove watchlist item');
                }
            }
            
            // Remove from UI
            const element = document.querySelector(`[data-ticker="${ticker}"]`);
            if (element) {
                element.style.animation = 'slideOut 0.3s ease-in forwards';
                setTimeout(() => {
                    element.remove();
                    // Update watchlistData
                    watchlistData = watchlistData.filter(w => w.ticker !== ticker);
                    updateSummaryStats();
                    
                    // Show empty state if no watchlists left
                    if (watchlistData.length === 0) {
                        location.reload();
                    }
                }, 300);
            }
            
        } catch (error) {
            console.error('Error removing watchlist:', error);
            alert('Failed to remove watchlist. Please try again.');
        }
    }
    
    function showAddWatchModal() {
        createAddWatchModal();
    }
    
    function createAddWatchModal() {
        const modal = document.getElementById('addWatchModal');
        
        modal.innerHTML = `
            <div class="modal-overlay" onclick="closeAddWatchModal()">
                <div class="modal-container" onclick="event.stopPropagation()">
                    <!-- Modal Header -->
                    <div class="modal-header">
                        <div class="modal-title">
                            <i class="fas fa-plus-circle"></i>
                            <span>Add Stock to Watchlist</span>
                        </div>
                        <button class="modal-close" onclick="closeAddWatchModal()">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                    
                    <!-- Modal Body -->
                    <div class="modal-body">
                        <!-- Step Indicator -->
                        <div class="step-indicator">
                            <div class="step active" data-step="1">
                                <div class="step-number">1</div>
                                <div class="step-label">Search Stock</div>
                            </div>
                            <div class="step-line"></div>
                            <div class="step" data-step="2">
                                <div class="step-number">2</div>
                                <div class="step-label">Select Metrics</div>
                            </div>
                            <div class="step-line"></div>
                            <div class="step" data-step="3">
                                <div class="step-number">3</div>
                                <div class="step-label">Configure Alerts</div>
                            </div>
                        </div>
                        
                        <!-- Step 1: Search Stock -->
                        <div class="modal-step" id="step1" style="display: block;">
                            <div class="search-section">
                                <div class="search-header">
                                    <h3><i class="fas fa-search"></i> Search for a Company</h3>
                                    <p>Enter a company name or ticker symbol to get started</p>
                                </div>
                                <div class="search-container">
                                    <div class="search-input-wrapper">
                                        <i class="fas fa-search search-icon"></i>
                                        <input type="text" id="stockSearch" placeholder="e.g., Apple, AAPL, Microsoft, MSFT..." autocomplete="off">
                                        <div class="search-loading hidden" id="searchLoading">
                                            <i class="fas fa-spinner fa-spin"></i>
                                        </div>
                                    </div>
                                    <div class="search-results" id="searchResults"></div>
                                </div>
                                <div class="popular-stocks">
                                    <h4>Popular Stocks</h4>
                                    <div class="popular-grid">
                                        <div class="popular-stock" onclick="selectStock('AAPL', 'Apple Inc.')">
                                            <i class="fab fa-apple"></i>
                                            <div>
                                                <div class="ticker">AAPL</div>
                                                <div class="name">Apple Inc.</div>
                                            </div>
                                        </div>
                                        <div class="popular-stock" onclick="selectStock('MSFT', 'Microsoft Corporation')">
                                            <i class="fab fa-microsoft"></i>
                                            <div>
                                                <div class="ticker">MSFT</div>
                                                <div class="name">Microsoft</div>
                                            </div>
                                        </div>
                                        <div class="popular-stock" onclick="selectStock('GOOGL', 'Alphabet Inc.')">
                                            <i class="fab fa-google"></i>
                                            <div>
                                                <div class="ticker">GOOGL</div>
                                                <div class="name">Alphabet</div>
                                            </div>
                                        </div>
                                        <div class="popular-stock" onclick="selectStock('TSLA', 'Tesla, Inc.')">
                                            <i class="fas fa-car"></i>
                                            <div>
                                                <div class="ticker">TSLA</div>
                                                <div class="name">Tesla</div>
                                            </div>
                                        </div>
                                        <div class="popular-stock" onclick="selectStock('AMZN', 'Amazon.com Inc.')">
                                            <i class="fab fa-amazon"></i>
                                            <div>
                                                <div class="ticker">AMZN</div>
                                                <div class="name">Amazon</div>
                                            </div>
                                        </div>
                                        <div class="popular-stock" onclick="selectStock('NFLX', 'Netflix Inc.')">
                                            <i class="fas fa-play"></i>
                                            <div>
                                                <div class="ticker">NFLX</div>
                                                <div class="name">Netflix</div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Step 2: Select Metrics -->
                        <div class="modal-step" id="step2" style="display: none;">
                            <div class="metrics-section">
                                <div class="selected-stock-info" id="selectedStockInfo"></div>
                                <div class="metrics-header">
                                    <h3><i class="fas fa-chart-bar"></i> Select Metrics to Track</h3>
                                    <p>Choose which financial metrics you want to monitor for this stock</p>
                                </div>
                                <div class="metrics-categories">
                                    <div class="metric-category active" data-category="price">
                                        <i class="fas fa-dollar-sign"></i>
                                        <span>Price & Volume</span>
                                    </div>
                                    <div class="metric-category" data-category="fundamental">
                                        <i class="fas fa-calculator"></i>
                                        <span>Fundamentals</span>
                                    </div>
                                    <div class="metric-category" data-category="technical">
                                        <i class="fas fa-chart-line"></i>
                                        <span>Technical</span>
                                    </div>
                                </div>
                                <div class="metrics-grid" id="metricsGrid"></div>
                            </div>
                        </div>
                        
                        <!-- Step 3: Configure Alerts -->
                        <div class="modal-step" id="step3" style="display: none;">
                            <div class="alerts-section">
                                <div class="alerts-header">
                                    <h3><i class="fas fa-bell"></i> Configure Alerts & Forecasts</h3>
                                    <p>Set up intelligent alerts and price targets for your selected metrics</p>
                                </div>
                                <div class="alerts-grid" id="alertsGrid"></div>
                                <div class="forecast-section">
                                    <h4><i class="fas fa-crystal-ball"></i> AI-Powered Forecasts</h4>
                                    <div class="forecast-options">
                                        <label class="forecast-option">
                                            <input type="checkbox" id="enableForecasts">
                                            <span class="checkmark"></span>
                                            <div class="forecast-info">
                                                <div class="forecast-title">Enable AI Forecasts</div>
                                                <div class="forecast-description">Get intelligent price predictions based on market trends and fundamentals</div>
                                            </div>
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Modal Footer -->
                    <div class="modal-footer">
                        <button class="btn btn-secondary" id="prevBtn" onclick="previousStep()" style="display: none;">
                            <i class="fas fa-arrow-left"></i> Previous
                        </button>
                        <div class="btn-spacer"></div>
                        <button class="btn btn-secondary" onclick="closeAddWatchModal()">
                            Cancel
                        </button>
                        <button class="btn btn-primary" id="nextBtn" onclick="nextStep()" disabled>
                            Next <i class="fas fa-arrow-right"></i>
                        </button>
                        <button class="btn btn-success" id="finishBtn" onclick="finishAddWatch()" style="display: none;">
                            <i class="fas fa-check"></i> Add to Watchlist
                        </button>
                    </div>
                </div>
            </div>
        `;
        
        modal.classList.remove('hidden');
        modal.style.display = 'flex';
        document.body.style.overflow = 'hidden';
        
        // Initialize search functionality
        initializeSearch();
        initializeMetrics();
        
        // Animate modal in
        setTimeout(() => {
            modal.querySelector('.modal-container').style.opacity = '1';
            modal.querySelector('.modal-container').style.transform = 'translateY(0) scale(1)';
        }, 10);
    }
    
    function closeAddWatchModal() {
        const modal = document.getElementById('addWatchModal');
        const container = modal.querySelector('.modal-container');
        
        container.style.opacity = '0';
        container.style.transform = 'translateY(-20px) scale(0.95)';
        
        setTimeout(() => {
            modal.classList.add('hidden');
            modal.style.display = 'none';
            document.body.style.overflow = '';
            resetModalState();
        }, 300);
    }
    
    function resetModalState() {
        currentStep = 1;
        selectedStock = null;
        selectedMetrics = [];
        modalAlerts = [];
    }
    
    let currentStep = 1;
    let selectedStock = null;
    let selectedMetrics = [];
    let modalAlerts = [];
    
    function nextStep() {
        if (currentStep < 3) {
            document.getElementById(`step${currentStep}`).style.display = 'none';
            currentStep++;
            document.getElementById(`step${currentStep}`).style.display = 'block';
            updateStepIndicator();
            updateButtons();
            
            if (currentStep === 2) {
                updateSelectedStockInfo();
            } else if (currentStep === 3) {
                initializeAlerts();
            }
        }
    }
    
    function previousStep() {
        if (currentStep > 1) {
            document.getElementById(`step${currentStep}`).style.display = 'none';
            currentStep--;
            document.getElementById(`step${currentStep}`).style.display = 'block';
            updateStepIndicator();
            updateButtons();
        }
    }
    
    function updateStepIndicator() {
        document.querySelectorAll('.step').forEach((step, index) => {
            if (index + 1 <= currentStep) {
                step.classList.add('active');
            } else {
                step.classList.remove('active');
            }
        });
    }
    
    function updateButtons() {
        const prevBtn = document.getElementById('prevBtn');
        const nextBtn = document.getElementById('nextBtn');
        const finishBtn = document.getElementById('finishBtn');
        
        // Previous button
        prevBtn.style.display = currentStep > 1 ? 'inline-flex' : 'none';
        
        // Next/Finish buttons
        if (currentStep === 3) {
            nextBtn.style.display = 'none';
            finishBtn.style.display = 'inline-flex';
        } else {
            nextBtn.style.display = 'inline-flex';
            finishBtn.style.display = 'none';
            
            // Enable/disable next button based on step completion
            if (currentStep === 1) {
                nextBtn.disabled = !selectedStock;
            } else if (currentStep === 2) {
                nextBtn.disabled = selectedMetrics.length === 0;
            }
        }
    }
    
    // Cleanup on page unload
    window.addEventListener('beforeunload', function() {
        if (refreshInterval) {
            clearInterval(refreshInterval);
        }
    });
    
    // Initialize search functionality
    function initializeSearch() {
        const searchInput = document.getElementById('stockSearch');\n        const searchResults = document.getElementById('searchResults');\n        const searchLoading = document.getElementById('searchLoading');\n        let searchTimeout;\n        \n        searchInput.addEventListener('input', function() {\n            const query = this.value.trim();\n            clearTimeout(searchTimeout);\n            \n            if (query.length < 2) {\n                searchResults.innerHTML = '';\n                return;\n            }\n            \n            searchLoading.classList.remove('hidden');\n            \n            searchTimeout = setTimeout(async () => {\n                await performSearch(query);\n                searchLoading.classList.add('hidden');\n            }, 300);\n        });\n    }\n    \n    async function performSearch(query) {\n        const searchResults = document.getElementById('searchResults');\n        \n        try {\n            // Enhanced stock database for better search results\n            const mockResults = [\n                { ticker: 'AAPL', name: 'Apple Inc.', sector: 'Technology' },\n                { ticker: 'MSFT', name: 'Microsoft Corporation', sector: 'Technology' },\n                { ticker: 'GOOGL', name: 'Alphabet Inc.', sector: 'Technology' },\n                { ticker: 'AMZN', name: 'Amazon.com Inc.', sector: 'Consumer Discretionary' },\n                { ticker: 'TSLA', name: 'Tesla, Inc.', sector: 'Consumer Discretionary' },\n                { ticker: 'NFLX', name: 'Netflix Inc.', sector: 'Communication Services' },\n                { ticker: 'META', name: 'Meta Platforms Inc.', sector: 'Communication Services' },\n                { ticker: 'NVDA', name: 'NVIDIA Corporation', sector: 'Technology' },\n                { ticker: 'JPM', name: 'JPMorgan Chase & Co.', sector: 'Financial Services' },\n                { ticker: 'JNJ', name: 'Johnson & Johnson', sector: 'Healthcare' },\n                { ticker: 'V', name: 'Visa Inc.', sector: 'Financial Services' },\n                { ticker: 'PG', name: 'Procter & Gamble Co.', sector: 'Consumer Staples' },\n                { ticker: 'UNH', name: 'UnitedHealth Group Inc.', sector: 'Healthcare' },\n                { ticker: 'HD', name: 'Home Depot Inc.', sector: 'Consumer Discretionary' },\n                { ticker: 'MA', name: 'Mastercard Inc.', sector: 'Financial Services' },\n                { ticker: 'BAC', name: 'Bank of America Corp.', sector: 'Financial Services' },\n                { ticker: 'DIS', name: 'Walt Disney Co.', sector: 'Communication Services' },\n                { ticker: 'ADBE', name: 'Adobe Inc.', sector: 'Technology' },\n                { ticker: 'CRM', name: 'Salesforce Inc.', sector: 'Technology' },\n                { ticker: 'PYPL', name: 'PayPal Holdings Inc.', sector: 'Financial Services' },\n                { ticker: 'INTC', name: 'Intel Corporation', sector: 'Technology' },\n                { ticker: 'CMCSA', name: 'Comcast Corporation', sector: 'Communication Services' },\n                { ticker: 'PFE', name: 'Pfizer Inc.', sector: 'Healthcare' },\n                { ticker: 'XOM', name: 'Exxon Mobil Corporation', sector: 'Energy' },\n                { ticker: 'KO', name: 'Coca-Cola Company', sector: 'Consumer Staples' },\n                { ticker: 'IBM', name: 'International Business Machines Corporation', sector: 'Technology' }\n            ].filter(stock => {\n                const searchTerm = query.toLowerCase();\n                const stockName = stock.name.toLowerCase();\n                const stockTicker = stock.ticker.toLowerCase();\n                const stockSector = stock.sector.toLowerCase();\n                \n                // Check for exact matches first\n                if (stockTicker === searchTerm || stockName === searchTerm) {\n                    return true;\n                }\n                \n                // Check for partial matches\n                if (stockTicker.includes(searchTerm) || stockName.includes(searchTerm) || stockSector.includes(searchTerm)) {\n                    return true;\n                }\n                \n                // Check for word matches (split search term)\n                const searchWords = searchTerm.split(' ');\n                return searchWords.some(word => {\n                    if (word.length < 2) return false;\n                    return stockName.includes(word) || stockTicker.includes(word) || stockSector.includes(word);\n                });\n            }).sort((a, b) => {\n                // Sort by relevance: exact matches first, then partial matches\n                const aName = a.name.toLowerCase();\n                const bName = b.name.toLowerCase();\n                const aTicker = a.ticker.toLowerCase();\n                const bTicker = b.ticker.toLowerCase();\n                const searchLower = query.toLowerCase();\n                \n                // Exact ticker match gets highest priority\n                if (aTicker === searchLower) return -1;\n                if (bTicker === searchLower) return 1;\n                \n                // Exact name match gets second priority\n                if (aName === searchLower) return -1;\n                if (bName === searchLower) return 1;\n                \n                // Ticker starts with search term\n                if (aTicker.startsWith(searchLower) && !bTicker.startsWith(searchLower)) return -1;\n                if (bTicker.startsWith(searchLower) && !aTicker.startsWith(searchLower)) return 1;\n                \n                // Name starts with search term\n                if (aName.startsWith(searchLower) && !bName.startsWith(searchLower)) return -1;\n                if (bName.startsWith(searchLower) && !aName.startsWith(searchLower)) return 1;\n                \n                // Default alphabetical sort\n                return aName.localeCompare(bName);\n            }).slice(0, 8); // Limit to 8 results\n            \n            if (mockResults.length === 0) {\n                searchResults.innerHTML = '<div class=\"no-results\">No stocks found matching \"' + query + '\"</div>';\n                return;\n            }\n            \n            searchResults.innerHTML = mockResults.map(stock => `\n                <div class=\"search-result\" onclick=\"selectStock('${stock.ticker}', '${stock.name}')\">\n                    <div class=\"stock-info\">\n                        <div class=\"stock-ticker\">${stock.ticker}</div>\n                        <div class=\"stock-name\">${stock.name}</div>\n                        <div class=\"stock-sector\">${stock.sector}</div>\n                    </div>\n                    <i class=\"fas fa-plus\"></i>\n                </div>\n            `).join('');\n            \n        } catch (error) {\n            console.error('Search error:', error);\n            searchResults.innerHTML = '<div class=\"search-error\">Error searching stocks. Please try again.</div>';\n        }\n    }\n    \n    function selectStock(ticker, name) {\n        selectedStock = { ticker, name };\n        \n        // Update UI to show selected stock\n        const searchInput = document.getElementById('stockSearch');\n        searchInput.value = `${ticker} - ${name}`;\n        \n        // Clear search results\n        document.getElementById('searchResults').innerHTML = '';\n        \n        // Enable next button\n        document.getElementById('nextBtn').disabled = false;\n        \n        // Add selected effect\n        searchInput.style.borderColor = 'var(--success)';\n        searchInput.style.backgroundColor = 'rgba(16, 185, 129, 0.05)';\n    }\n    \n    function updateSelectedStockInfo() {\n        const infoContainer = document.getElementById('selectedStockInfo');\n        if (selectedStock) {\n            infoContainer.innerHTML = `\n                <div class=\"selected-stock\">\n                    <div class=\"stock-avatar\">\n                        <i class=\"fas fa-building\"></i>\n                    </div>\n                    <div class=\"stock-details\">\n                        <div class=\"stock-ticker\">${selectedStock.ticker}</div>\n                        <div class=\"stock-name\">${selectedStock.name}</div>\n                    </div>\n                    <div class=\"stock-status\">\n                        <i class=\"fas fa-check-circle\"></i>\n                        Selected\n                    </div>\n                </div>\n            `;\n        }\n    }\n    \n    function initializeMetrics() {\n        const categories = {\n            price: [\n                { id: 'current_price', name: 'Current Price', description: 'Real-time stock price', icon: 'fas fa-dollar-sign' },\n                { id: 'day_change', name: 'Day Change %', description: 'Daily price change percentage', icon: 'fas fa-chart-line' },\n                { id: 'volume', name: 'Volume', description: 'Trading volume', icon: 'fas fa-chart-bar' },\n                { id: 'market_cap', name: 'Market Cap', description: 'Total market capitalization', icon: 'fas fa-coins' }\n            ],\n            fundamental: [\n                { id: 'pe_ratio', name: 'P/E Ratio', description: 'Price-to-earnings ratio', icon: 'fas fa-calculator' },\n                { id: 'price_to_book', name: 'P/B Ratio', description: 'Price-to-book ratio', icon: 'fas fa-book' },\n                { id: 'total_revenue', name: 'Revenue', description: 'Total company revenue', icon: 'fas fa-chart-area' },\n                { id: 'net_income', name: 'Net Income', description: 'Company net income', icon: 'fas fa-money-bill' }\n            ],\n            technical: [\n                { id: 'rsi', name: 'RSI', description: 'Relative Strength Index', icon: 'fas fa-signal' },\n                { id: 'moving_avg_50', name: '50-Day MA', description: '50-day moving average', icon: 'fas fa-wave-square' },\n                { id: 'moving_avg_200', name: '200-Day MA', description: '200-day moving average', icon: 'fas fa-chart-line' },\n                { id: 'bollinger_upper', name: 'Bollinger Upper', description: 'Bollinger Band upper limit', icon: 'fas fa-arrows-alt-v' }\n            ]\n        };\n        \n        // Initialize category switching\n        document.querySelectorAll('.metric-category').forEach(category => {\n            category.addEventListener('click', function() {\n                document.querySelectorAll('.metric-category').forEach(c => c.classList.remove('active'));\n                this.classList.add('active');\n                \n                const categoryType = this.dataset.category;\n                displayMetrics(categories[categoryType]);\n            });\n        });\n        \n        // Display default metrics (price)\n        displayMetrics(categories.price);\n    }\n    \n    function displayMetrics(metrics) {\n        const metricsGrid = document.getElementById('metricsGrid');\n        \n        metricsGrid.innerHTML = metrics.map(metric => `\n            <div class=\"metric-option ${selectedMetrics.includes(metric.id) ? 'selected' : ''}\" \n                 onclick=\"toggleMetric('${metric.id}')\" data-metric=\"${metric.id}\">\n                <div class=\"metric-icon\">\n                    <i class=\"${metric.icon}\"></i>\n                </div>\n                <div class=\"metric-info\">\n                    <div class=\"metric-name\">${metric.name}</div>\n                    <div class=\"metric-description\">${metric.description}</div>\n                </div>\n                <div class=\"metric-checkbox\">\n                    <i class=\"fas fa-check\"></i>\n                </div>\n            </div>\n        `).join('');\n    }\n    \n    function toggleMetric(metricId) {\n        const metricElement = document.querySelector(`[data-metric=\"${metricId}\"]`);\n        \n        if (selectedMetrics.includes(metricId)) {\n            selectedMetrics = selectedMetrics.filter(id => id !== metricId);\n            metricElement.classList.remove('selected');\n        } else {\n            selectedMetrics.push(metricId);\n            metricElement.classList.add('selected');\n        }\n        \n        // Update next button state\n        document.getElementById('nextBtn').disabled = selectedMetrics.length === 0;\n        \n        // Add selection animation\n        metricElement.style.transform = 'scale(0.95)';\n        setTimeout(() => {\n            metricElement.style.transform = 'scale(1)';\n        }, 150);\n    }\n    \n    function initializeAlerts() {\n        const alertsGrid = document.getElementById('alertsGrid');\n        \n        alertsGrid.innerHTML = selectedMetrics.map(metricId => `\n            <div class=\"alert-config\" data-metric=\"${metricId}\">\n                <div class=\"alert-header\">\n                    <h4>${metricId.replace('_', ' ').replace(/\\b\\w/g, l => l.toUpperCase())}</h4>\n                    <label class=\"alert-toggle\">\n                        <input type=\"checkbox\" onchange=\"toggleAlert('${metricId}')\">\n                        <span class=\"slider\"></span>\n                    </label>\n                </div>\n                <div class=\"alert-settings\" style=\"display: none;\">\n                    <div class=\"condition-row\">\n                        <select class=\"condition-type\" data-metric=\"${metricId}\">\n                            <option value=\"greater_than\">Greater than</option>\n                            <option value=\"less_than\">Less than</option>\n                            <option value=\"percent_change\">% Change</option>\n                        </select>\n                        <input type=\"number\" class=\"target-value\" placeholder=\"Target value\" data-metric=\"${metricId}\">\n                    </div>\n                    <div class=\"notification-options\">\n                        <label><input type=\"checkbox\" checked> Email notification</label>\n                        <label><input type=\"checkbox\"> Push notification</label>\n                    </div>\n                </div>\n            </div>\n        `).join('');\n    }\n    \n    function toggleAlert(metricId) {\n        const alertConfig = document.querySelector(`[data-metric=\"${metricId}\"]`);\n        const settings = alertConfig.querySelector('.alert-settings');\n        const isEnabled = alertConfig.querySelector('input[type=\"checkbox\"]').checked;\n        \n        if (isEnabled) {\n            settings.style.display = 'block';\n            settings.style.animation = 'slideDown 0.3s ease-out';\n        } else {\n            settings.style.animation = 'slideUp 0.3s ease-out';\n            setTimeout(() => {\n                settings.style.display = 'none';\n            }, 300);\n        }\n    }\n    \n    async function finishAddWatch() {\n        const finishBtn = document.getElementById('finishBtn');\n        const originalText = finishBtn.innerHTML;\n        \n        // Show loading state\n        finishBtn.innerHTML = '<i class=\"fas fa-spinner fa-spin\"></i> Adding...';\n        finishBtn.disabled = true;\n        \n        try {\n            // Collect alert data\n            const alerts = [];\n            selectedMetrics.forEach(metricId => {\n                const alertConfig = document.querySelector(`[data-metric=\"${metricId}\"]`);\n                const isEnabled = alertConfig?.querySelector('input[type=\"checkbox\"]')?.checked;\n                \n                if (isEnabled) {\n                    const conditionType = alertConfig.querySelector('.condition-type').value;\n                    const targetValue = alertConfig.querySelector('.target-value').value;\n                    \n                    if (targetValue) {\n                        alerts.push({\n                            metric: metricId,\n                            condition: conditionType,\n                            target: parseFloat(targetValue)\n                        });\n                    }\n                }\n            });\n            \n            // Add to watchlist\n            for (const metricId of selectedMetrics) {\n                const alert = alerts.find(a => a.metric === metricId);\n                \n                const response = await fetch('/watchlist', {\n                    method: 'POST',\n                    headers: {\n                        'Content-Type': 'application/json'\n                    },\n                    body: JSON.stringify({\n                        ticker: selectedStock.ticker,\n                        metric: metricId,\n                        condition: alert?.condition || 'greater_than',\n                        target_value: alert?.target || 0,\n                        description: `${selectedStock.name} - ${metricId.replace('_', ' ')}`\n                    })\n                });\n                \n                if (!response.ok) {\n                    throw new Error('Failed to add watchlist item');\n                }\n            }\n            \n            // Success animation\n            finishBtn.innerHTML = '<i class=\"fas fa-check\"></i> Added Successfully!';\n            finishBtn.style.backgroundColor = 'var(--success)';\n            \n            setTimeout(() => {\n                closeAddWatchModal();\n                location.reload(); // Refresh to show new watchlist items\n            }, 1500);\n            \n        } catch (error) {\n            console.error('Error adding to watchlist:', error);\n            finishBtn.innerHTML = '<i class=\"fas fa-exclamation-triangle\"></i> Error - Try Again';\n            finishBtn.style.backgroundColor = 'var(--danger)';\n            finishBtn.disabled = false;\n            \n            setTimeout(() => {\n                finishBtn.innerHTML = originalText;\n                finishBtn.style.backgroundColor = '';\n            }, 3000);\n        }\n    }\n    \n    function getMetricType(metricId) {\n        const types = {\n            current_price: 'price',\n            day_change: 'price',\n            volume: 'volume',\n            market_cap: 'fundamental',\n            pe_ratio: 'fundamental',\n            price_to_book: 'fundamental',\n            total_revenue: 'fundamental',\n            net_income: 'fundamental',\n            rsi: 'technical',\n            moving_avg_50: 'technical',\n            moving_avg_200: 'technical',\n            bollinger_upper: 'technical'\n        };\n        return types[metricId] || 'other';\n    }\n    \n    // Add sparkle animation keyframes\n    const style = document.createElement('style');
    style.textContent = `
        @keyframes sparkle {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); box-shadow: 0 0 10px var(--accent-primary); }
            100% { transform: scale(1); }
        }
        
        @keyframes slideOut {
            to {
                transform: translateX(-100%);
                opacity: 0;
            }
        }
        
        @keyframes slideDown {
            from {
                opacity: 0;
                max-height: 0;
                transform: translateY(-10px);
            }
            to {
                opacity: 1;
                max-height: 200px;
                transform: translateY(0);
            }
        }
        
        @keyframes slideUp {
            from {
                opacity: 1;
                max-height: 200px;
                transform: translateY(0);
            }
            to {
                opacity: 0;
                max-height: 0;
                transform: translateY(-10px);
            }
        }
    `;
    document.head.appendChild(style);
</script>
{% endblock %}