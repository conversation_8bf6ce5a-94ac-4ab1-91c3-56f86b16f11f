{% extends "base.html" %}

{% block title %}Server Error - Stock Watchlist Dashboard{% endblock %}

{% block content %}
<div class="text-center" style="padding: 4rem 0;">
    <div style="font-size: 6rem; margin-bottom: 1rem;">
        <i class="fas fa-exclamation-triangle text-warning"></i>
    </div>
    
    <h1 style="font-size: 2.5rem; font-weight: 700; margin-bottom: 1rem; color: var(--text-primary);">
        Server Error
    </h1>
    
    <p style="font-size: 1.25rem; color: var(--text-secondary); margin-bottom: 2rem; max-width: 500px; margin-left: auto; margin-right: auto;">
        Something went wrong on our end. We're working to fix this issue. Please try again in a few moments.
    </p>
    
    <div style="display: flex; gap: 1rem; justify-content: center; flex-wrap: wrap;">
        <a href="{{ url_for('index') }}" 
           style="background-color: var(--accent-primary); color: white; padding: 0.75rem 1.5rem; border-radius: var(--radius-lg); text-decoration: none; font-weight: 600; transition: all var(--transition-fast);"
           onmouseover="this.style.backgroundColor='var(--accent-secondary)'"
           onmouseout="this.style.backgroundColor='var(--accent-primary)'">
            <i class="fas fa-home"></i> Back to Dashboard
        </a>
        
        <button onclick="location.reload()" 
                style="background-color: var(--bg-primary); color: var(--text-primary); border: 1px solid var(--border-primary); padding: 0.75rem 1.5rem; border-radius: var(--radius-lg); font-weight: 600; cursor: pointer; transition: all var(--transition-fast);"
                onmouseover="this.style.backgroundColor='var(--bg-tertiary)'"
                onmouseout="this.style.backgroundColor='var(--bg-primary)'">
            <i class="fas fa-redo"></i> Try Again
        </button>
    </div>
    
    <!-- Error details (only show in development) -->
    {% if config.DEBUG %}
    <div style="margin-top: 3rem; padding: 2rem; background-color: var(--bg-primary); border-radius: var(--radius-xl); max-width: 800px; margin-left: auto; margin-right: auto; border: 1px solid var(--border-primary); text-align: left;">
        <h3 style="margin-bottom: 1rem; color: var(--text-primary);">Debug Information</h3>
        <div style="background-color: var(--bg-secondary); padding: 1rem; border-radius: var(--radius-md); font-family: monospace; font-size: 0.875rem; color: var(--text-secondary); overflow-x: auto;">
            <p><strong>Error:</strong> Internal Server Error (500)</p>
            <p><strong>Time:</strong> {{ moment().format('YYYY-MM-DD HH:mm:ss') }}</p>
            <p><strong>Endpoint:</strong> {{ request.endpoint }}</p>
            <p><strong>Method:</strong> {{ request.method }}</p>
            <p><strong>URL:</strong> {{ request.url }}</p>
        </div>
    </div>
    {% endif %}
    
    <!-- Support information -->
    <div style="margin-top: 3rem; padding: 2rem; background-color: var(--bg-primary); border-radius: var(--radius-xl); max-width: 600px; margin-left: auto; margin-right: auto; border: 1px solid var(--border-primary);">
        <h3 style="margin-bottom: 1rem; color: var(--text-primary);">Need Help?</h3>
        <p style="color: var(--text-secondary); margin-bottom: 1rem;">
            If this problem persists, you can:
        </p>
        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1rem;">
            <div style="padding: 1rem; background-color: var(--bg-secondary); border-radius: var(--radius-md);">
                <i class="fas fa-clock text-info" style="font-size: 1.5rem; margin-bottom: 0.5rem;"></i>
                <div style="font-weight: 600; margin-bottom: 0.25rem;">Wait a moment</div>
                <div style="font-size: 0.875rem; color: var(--text-muted);">The issue might resolve itself</div>
            </div>
            <div style="padding: 1rem; background-color: var(--bg-secondary); border-radius: var(--radius-md);">
                <i class="fas fa-redo text-warning" style="font-size: 1.5rem; margin-bottom: 0.5rem;"></i>
                <div style="font-weight: 600; margin-bottom: 0.25rem;">Refresh the page</div>
                <div style="font-size: 0.875rem; color: var(--text-muted);">Sometimes a simple refresh helps</div>
            </div>
            <div style="padding: 1rem; background-color: var(--bg-secondary); border-radius: var(--radius-md);">
                <i class="fas fa-history text-success" style="font-size: 1.5rem; margin-bottom: 0.5rem;"></i>
                <div style="font-weight: 600; margin-bottom: 0.25rem;">Try again later</div>
                <div style="font-size: 0.875rem; color: var(--text-muted);">Check back in a few minutes</div>
            </div>
        </div>
    </div>
</div>
{% endblock %}