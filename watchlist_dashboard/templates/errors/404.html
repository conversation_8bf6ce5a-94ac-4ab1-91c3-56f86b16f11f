{% extends "base.html" %}

{% block title %}Page Not Found - Stock Watchlist Dashboard{% endblock %}

{% block content %}
<div class="text-center" style="padding: 4rem 0;">
    <div style="font-size: 6rem; margin-bottom: 1rem;">
        <i class="fas fa-search text-muted"></i>
    </div>
    
    <h1 style="font-size: 2.5rem; font-weight: 700; margin-bottom: 1rem; color: var(--text-primary);">
        Page Not Found
    </h1>
    
    <p style="font-size: 1.25rem; color: var(--text-secondary); margin-bottom: 2rem; max-width: 500px; margin-left: auto; margin-right: auto;">
        The page you're looking for doesn't exist. It might have been moved, deleted, or you entered the wrong URL.
    </p>
    
    <div style="display: flex; gap: 1rem; justify-content: center; flex-wrap: wrap;">
        <a href="{{ url_for('index') }}" 
           style="background-color: var(--accent-primary); color: white; padding: 0.75rem 1.5rem; border-radius: var(--radius-lg); text-decoration: none; font-weight: 600; transition: all var(--transition-fast);"
           onmouseover="this.style.backgroundColor='var(--accent-secondary)'"
           onmouseout="this.style.backgroundColor='var(--accent-primary)'">
            <i class="fas fa-home"></i> Back to Dashboard
        </a>
        
        <button onclick="history.back()" 
                style="background-color: var(--bg-primary); color: var(--text-primary); border: 1px solid var(--border-primary); padding: 0.75rem 1.5rem; border-radius: var(--radius-lg); font-weight: 600; cursor: pointer; transition: all var(--transition-fast);"
                onmouseover="this.style.backgroundColor='var(--bg-tertiary)'"
                onmouseout="this.style.backgroundColor='var(--bg-primary)'">
            <i class="fas fa-arrow-left"></i> Go Back
        </button>
    </div>
    
    <!-- Helpful links -->
    <div style="margin-top: 3rem; padding: 2rem; background-color: var(--bg-primary); border-radius: var(--radius-xl); max-width: 600px; margin-left: auto; margin-right: auto; border: 1px solid var(--border-primary);">
        <h3 style="margin-bottom: 1rem; color: var(--text-primary);">Quick Links</h3>
        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1rem;">
            <a href="{{ url_for('index') }}" style="text-decoration: none; color: var(--text-secondary); padding: 0.5rem; border-radius: var(--radius-md); transition: all var(--transition-fast);"
               onmouseover="this.style.backgroundColor='var(--bg-tertiary)'; this.style.color='var(--accent-primary)'"
               onmouseout="this.style.backgroundColor='transparent'; this.style.color='var(--text-secondary)'">
                <i class="fas fa-chart-line"></i> Dashboard
            </a>
            <a href="#" onclick="showAddWatchModal(); return false;" style="text-decoration: none; color: var(--text-secondary); padding: 0.5rem; border-radius: var(--radius-md); transition: all var(--transition-fast);"
               onmouseover="this.style.backgroundColor='var(--bg-tertiary)'; this.style.color='var(--accent-primary)'"
               onmouseout="this.style.backgroundColor='transparent'; this.style.color='var(--text-secondary)'">
                <i class="fas fa-plus"></i> Add Watch
            </a>
            <a href="#" onclick="toggleNotifications(); return false;" style="text-decoration: none; color: var(--text-secondary); padding: 0.5rem; border-radius: var(--radius-md); transition: all var(--transition-fast);"
               onmouseover="this.style.backgroundColor='var(--bg-tertiary)'; this.style.color='var(--accent-primary)'"
               onmouseout="this.style.backgroundColor='transparent'; this.style.color='var(--text-secondary)'">
                <i class="fas fa-bell"></i> Notifications
            </a>
        </div>
    </div>
</div>
{% endblock %}