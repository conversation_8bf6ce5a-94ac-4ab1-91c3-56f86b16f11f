"""
Watchlist Service for Stock Watchlist Dashboard

This service manages watchlist operations including:
- Adding/removing watchlist items
- Managing user forecasts
- Checking watch conditions
- Organizing watchlists by categories
"""

import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from models.database import WatchlistItemModel, ForecastModel

logger = logging.getLogger(__name__)

class WatchlistService:
    """Service for managing watchlists and forecasts"""
    
    def __init__(self):
        self.watchlist_model = WatchlistItemModel()
        self.forecast_model = ForecastModel()
    
    def get_user_watchlists(self, user_id: str) -> List[Dict[str, Any]]:
        """Get all watchlists for a user, organized by ticker"""
        try:
            # Get all watchlist items
            items = self.watchlist_model.get_by_user(user_id)
            
            # Group by ticker
            watchlists = {}
            for item in items:
                ticker = item['ticker']
                
                if ticker not in watchlists:
                    watchlists[ticker] = {
                        'ticker': ticker,
                        'company_name': item['company_name'],
                        'metrics': [],
                        'alerts': [],
                        'forecasts': []
                    }
                
                # Add metric information
                metric_info = {
                    'id': item['id'],
                    'metric_type': item['metric_type'],
                    'metric_name': item['metric_name'],
                    'condition_type': item['condition_type'],
                    'target_value': item['target_value'],
                    'description': item['description'],
                    'created_at': item['created_at']
                }
                
                watchlists[ticker]['metrics'].append(metric_info)
                
                # If it has a condition, it's an alert
                if item['condition_type'] and item['target_value']:
                    watchlists[ticker]['alerts'].append(metric_info)
            
            # Get forecasts for each ticker
            for ticker in watchlists.keys():
                forecasts = self.forecast_model.get_by_user(user_id)
                ticker_forecasts = [f for f in forecasts if f['ticker'] == ticker]
                watchlists[ticker]['forecasts'] = ticker_forecasts
            
            return list(watchlists.values())
            
        except Exception as e:
            logger.error(f"Error getting user watchlists: {e}")
            return []
    
    def add_watch_item(self, user_id: str, ticker: str, metric: str, 
                      condition: str, target_value: float = None, 
                      description: str = '', company_name: str = '') -> bool:
        """Add a new item to watchlist"""
        try:
            # Determine metric type and name from metric string
            metric_info = self._parse_metric_info(metric)
            
            item_id = self.watchlist_model.create(
                user_id=user_id,
                ticker=ticker.upper(),
                company_name=company_name,
                metric_type=metric_info['type'],
                metric_name=metric_info['name'],
                condition_type=condition,
                target_value=target_value,
                description=description
            )
            
            return item_id is not None
            
        except Exception as e:
            logger.error(f"Error adding watch item: {e}")
            return False
    
    def remove_watch_item(self, user_id: str, item_id: int) -> bool:
        """Remove item from watchlist"""
        try:
            return self.watchlist_model.delete(user_id, item_id)
        except Exception as e:
            logger.error(f"Error removing watch item: {e}")
            return False
    
    def save_forecast(self, user_id: str, ticker: str, metric: str, 
                     forecast_value: float, forecast_date: str, 
                     description: str = '') -> bool:
        """Save a user forecast"""
        try:
            forecast_id = self.forecast_model.create(
                user_id=user_id,
                ticker=ticker.upper(),
                metric_name=metric,
                forecast_value=forecast_value,
                forecast_date=forecast_date,
                description=description
            )
            
            return forecast_id is not None
            
        except Exception as e:
            logger.error(f"Error saving forecast: {e}")
            return False
    
    def get_user_forecasts(self, user_id: str, ticker: str = None) -> List[Dict[str, Any]]:
        """Get user forecasts, optionally filtered by ticker"""
        try:
            if ticker:
                # Get forecasts for specific ticker
                all_forecasts = self.forecast_model.get_by_user(user_id)
                return [f for f in all_forecasts if f['ticker'] == ticker.upper()]
            else:
                return self.forecast_model.get_by_user(user_id)
                
        except Exception as e:
            logger.error(f"Error getting user forecasts: {e}")
            return []
    
    def check_watch_conditions(self, user_id: str, ticker: str, 
                              current_data: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Check if any watch conditions are met for a ticker"""
        try:
            alerts_triggered = []
            
            # Get watchlist items for this ticker
            items = self.watchlist_model.get_by_ticker(user_id, ticker)
            
            for item in items:
                if not item['is_active'] or not item['condition_type']:
                    continue
                
                metric_name = item['metric_name']
                condition_type = item['condition_type']
                target_value = item['target_value']
                
                # Get current value for this metric
                current_value = current_data.get(metric_name)
                if current_value is None:
                    continue
                
                # Check condition
                condition_met = self._check_condition(
                    current_value, condition_type, target_value
                )
                
                if condition_met:
                    alerts_triggered.append({
                        'item_id': item['id'],
                        'ticker': ticker,
                        'metric_name': metric_name,
                        'condition_type': condition_type,
                        'target_value': target_value,
                        'current_value': current_value,
                        'description': item['description'],
                        'triggered_at': datetime.utcnow().isoformat()
                    })
            
            return alerts_triggered
            
        except Exception as e:
            logger.error(f"Error checking watch conditions: {e}")
            return []
    
    def check_forecast_achievements(self, user_id: str, ticker: str, 
                                   current_data: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Check if any forecasts have been achieved"""
        try:
            achievements = []
            
            # Get active forecasts for this ticker
            forecasts = self.forecast_model.get_by_user(user_id)
            ticker_forecasts = [f for f in forecasts 
                              if f['ticker'] == ticker.upper() and not f['is_achieved']]
            
            for forecast in ticker_forecasts:
                metric_name = forecast['metric_name']
                forecast_value = forecast['forecast_value']
                forecast_date = forecast['forecast_date']
                
                # Check if forecast date has passed
                try:
                    forecast_datetime = datetime.fromisoformat(forecast_date)
                    if forecast_datetime > datetime.utcnow():
                        continue  # Forecast date hasn't arrived yet
                except:
                    continue
                
                # Get current value for this metric
                current_value = current_data.get(metric_name)
                if current_value is None:
                    continue
                
                # Check if forecast is achieved (within 5% tolerance)
                tolerance = 0.05
                if abs(current_value - forecast_value) / forecast_value <= tolerance:
                    # Update forecast as achieved
                    self.forecast_model.update_achievement(
                        forecast['id'], current_value, datetime.utcnow().isoformat()
                    )
                    
                    achievements.append({
                        'forecast_id': forecast['id'],
                        'ticker': ticker,
                        'metric_name': metric_name,
                        'forecast_value': forecast_value,
                        'actual_value': current_value,
                        'forecast_date': forecast_date,
                        'achieved_at': datetime.utcnow().isoformat(),
                        'description': forecast['description']
                    })
            
            return achievements
            
        except Exception as e:
            logger.error(f"Error checking forecast achievements: {e}")
            return []
    
    def get_watchlist_summary(self, user_id: str) -> Dict[str, Any]:
        """Get summary statistics for user's watchlists"""
        try:
            watchlists = self.get_user_watchlists(user_id)
            forecasts = self.get_user_forecasts(user_id)
            
            total_stocks = len(watchlists)
            total_metrics = sum(len(w['metrics']) for w in watchlists)
            total_alerts = sum(len(w['alerts']) for w in watchlists)
            total_forecasts = len(forecasts)
            active_forecasts = len([f for f in forecasts if not f['is_achieved']])
            
            # Calculate category breakdown
            categories = {}
            for watchlist in watchlists:
                for metric in watchlist['metrics']:
                    cat = metric['metric_type']
                    categories[cat] = categories.get(cat, 0) + 1
            
            return {
                'total_stocks': total_stocks,
                'total_metrics': total_metrics,
                'total_alerts': total_alerts,
                'total_forecasts': total_forecasts,
                'active_forecasts': active_forecasts,
                'categories': categories,
                'watchlists': watchlists
            }
            
        except Exception as e:
            logger.error(f"Error getting watchlist summary: {e}")
            return {
                'total_stocks': 0,
                'total_metrics': 0,
                'total_alerts': 0,
                'total_forecasts': 0,
                'active_forecasts': 0,
                'categories': {},
                'watchlists': []
            }
    
    def _parse_metric_info(self, metric: str) -> Dict[str, str]:
        """Parse metric string to determine type and name"""
        try:
            # Map common metrics to their types
            metric_types = {
                'current_price': 'price',
                'day_change': 'price',
                'volume': 'price',
                'pe_ratio': 'valuation',
                'market_cap': 'valuation',
                'book_value': 'valuation',
                'price_to_book': 'valuation',
                'rsi': 'technical',
                'sma_50': 'technical',
                'sma_200': 'technical',
                'ema_12': 'technical',
                'total_revenue': 'fundamentals',
                'net_income': 'fundamentals',
                'free_cash_flow': 'fundamentals',
                'operating_cash_flow': 'fundamentals',
                'total_assets': 'fundamentals',
                'total_debt': 'fundamentals'
            }
            
            metric_type = metric_types.get(metric, 'other')
            
            return {
                'type': metric_type,
                'name': metric
            }
            
        except Exception as e:
            logger.error(f"Error parsing metric info: {e}")
            return {'type': 'other', 'name': metric}
    
    def _check_condition(self, current_value: float, condition_type: str, 
                        target_value: float) -> bool:
        """Check if a condition is met"""
        try:
            if condition_type == 'greater_than':
                return current_value > target_value
            elif condition_type == 'less_than':
                return current_value < target_value
            elif condition_type == 'equals':
                # Allow 1% tolerance for equals
                tolerance = 0.01
                return abs(current_value - target_value) / target_value <= tolerance
            elif condition_type == 'crosses_above':
                # This would require historical comparison
                # For now, treat as greater_than
                return current_value > target_value
            elif condition_type == 'crosses_below':
                # This would require historical comparison
                # For now, treat as less_than
                return current_value < target_value
            else:
                return False
                
        except Exception as e:
            logger.error(f"Error checking condition: {e}")
            return False