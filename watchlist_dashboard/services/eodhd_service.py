"""
EODHD API Service for Stock Watchlist Dashboard

This service handles all interactions with the EODHD API including:
- Company/ticker search
- Real-time and historical price data
- Fundamental data (P/E, revenue, etc.)
- Technical indicators (RSI, moving averages)
- Market data and metrics
"""

import os
import json
import logging
import requests
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from models.database import CacheModel

logger = logging.getLogger(__name__)

class EODHDService:
    """Service for interacting with EODHD API"""
    
    def __init__(self, api_key: str):
        self.api_key = api_key
        self.base_url = "https://eodhd.com/api"
        self.session = requests.Session()
        self.cache = CacheModel()
        
        if not self.api_key:
            logger.warning("EODHD API key not provided")
    
    def _make_request(self, endpoint: str, params: Dict[str, Any] = None) -> Optional[Dict]:
        """Make a request to EODHD API with error handling"""
        if not self.api_key:
            logger.error("EODHD API key not configured")
            return None
        
        try:
            url = f"{self.base_url}/{endpoint}"
            request_params = {'api_token': self.api_key, 'fmt': 'json'}
            
            if params:
                request_params.update(params)
            
            response = self.session.get(url, params=request_params, timeout=30)
            response.raise_for_status()
            
            return response.json()
            
        except requests.exceptions.RequestException as e:
            logger.error(f"EODHD API request failed for {endpoint}: {e}")
            return None
        except json.JSONDecodeError as e:
            logger.error(f"Failed to parse EODHD response for {endpoint}: {e}")
            return None
    
    def search_companies(self, query: str, limit: int = 20) -> List[Dict[str, Any]]:
        """Search for companies by name or ticker"""
        try:
            # Check cache first
            cache_key = f"search_{query.lower()}"
            cached_data = self.cache.get('search', cache_key)

            if cached_data:
                return json.loads(cached_data)

            # Try EODHD API first
            results = self._search_eodhd_api(query, limit)

            # If EODHD fails (demo key limitations), use fallback search
            if not results:
                results = self._search_fallback(query, limit)

            # Cache results for 1 hour
            if results:
                self.cache.set('search', cache_key, json.dumps(results),
                              datetime.utcnow() + timedelta(hours=1))

            return results

        except Exception as e:
            logger.error(f"Error searching companies: {e}")
            return self._search_fallback(query, limit)

    def _search_eodhd_api(self, query: str, limit: int) -> List[Dict[str, Any]]:
        """Search using EODHD API"""
        try:
            # Search using the correct search endpoint
            data = self._make_request(f'search/{query}')

            if not data:
                return []

            # Format results - limit to specified number
            results = []
            count = 0
            for item in data:
                if count >= limit:
                    break

                if isinstance(item, dict):
                    # Extract ticker and exchange info
                    code = item.get('Code', '')
                    exchange = item.get('Exchange', '')
                    name = item.get('Name', '')

                    # Skip if essential data is missing
                    if not code or not name:
                        continue

                    results.append({
                        'ticker': code,
                        'name': name,
                        'exchange': exchange,
                        'type': item.get('Type', ''),
                        'country': item.get('Country', ''),
                        'currency': item.get('Currency', ''),
                        'display_name': f"{name} ({code})"
                    })
                    count += 1

            return results

        except Exception as e:
            logger.error(f"EODHD API search failed: {e}")
            return []

    def _search_fallback(self, query: str, limit: int) -> List[Dict[str, Any]]:
        """Fallback search using popular stocks list when API is unavailable"""
        # Popular stocks database for fallback search
        popular_stocks = [
            {'ticker': 'AAPL', 'name': 'Apple Inc.', 'exchange': 'NASDAQ', 'country': 'US', 'currency': 'USD'},
            {'ticker': 'TSLA', 'name': 'Tesla, Inc.', 'exchange': 'NASDAQ', 'country': 'US', 'currency': 'USD'},
            {'ticker': 'MSFT', 'name': 'Microsoft Corporation', 'exchange': 'NASDAQ', 'country': 'US', 'currency': 'USD'},
            {'ticker': 'GOOGL', 'name': 'Alphabet Inc. Class A', 'exchange': 'NASDAQ', 'country': 'US', 'currency': 'USD'},
            {'ticker': 'GOOG', 'name': 'Alphabet Inc. Class C', 'exchange': 'NASDAQ', 'country': 'US', 'currency': 'USD'},
            {'ticker': 'AMZN', 'name': 'Amazon.com, Inc.', 'exchange': 'NASDAQ', 'country': 'US', 'currency': 'USD'},
            {'ticker': 'META', 'name': 'Meta Platforms, Inc.', 'exchange': 'NASDAQ', 'country': 'US', 'currency': 'USD'},
            {'ticker': 'NVDA', 'name': 'NVIDIA Corporation', 'exchange': 'NASDAQ', 'country': 'US', 'currency': 'USD'},
            {'ticker': 'NFLX', 'name': 'Netflix, Inc.', 'exchange': 'NASDAQ', 'country': 'US', 'currency': 'USD'},
            {'ticker': 'AMD', 'name': 'Advanced Micro Devices, Inc.', 'exchange': 'NASDAQ', 'country': 'US', 'currency': 'USD'},
            {'ticker': 'INTC', 'name': 'Intel Corporation', 'exchange': 'NASDAQ', 'country': 'US', 'currency': 'USD'},
            {'ticker': 'CRM', 'name': 'Salesforce, Inc.', 'exchange': 'NYSE', 'country': 'US', 'currency': 'USD'},
            {'ticker': 'ORCL', 'name': 'Oracle Corporation', 'exchange': 'NYSE', 'country': 'US', 'currency': 'USD'},
            {'ticker': 'ADBE', 'name': 'Adobe Inc.', 'exchange': 'NASDAQ', 'country': 'US', 'currency': 'USD'},
            {'ticker': 'PYPL', 'name': 'PayPal Holdings, Inc.', 'exchange': 'NASDAQ', 'country': 'US', 'currency': 'USD'},
            {'ticker': 'DIS', 'name': 'The Walt Disney Company', 'exchange': 'NYSE', 'country': 'US', 'currency': 'USD'},
            {'ticker': 'UBER', 'name': 'Uber Technologies, Inc.', 'exchange': 'NYSE', 'country': 'US', 'currency': 'USD'},
            {'ticker': 'SPOT', 'name': 'Spotify Technology S.A.', 'exchange': 'NYSE', 'country': 'US', 'currency': 'USD'},
            {'ticker': 'ZOOM', 'name': 'Zoom Video Communications, Inc.', 'exchange': 'NASDAQ', 'country': 'US', 'currency': 'USD'},
            {'ticker': 'SQ', 'name': 'Block, Inc.', 'exchange': 'NYSE', 'country': 'US', 'currency': 'USD'},
            {'ticker': 'TWTR', 'name': 'Twitter, Inc.', 'exchange': 'NYSE', 'country': 'US', 'currency': 'USD'},
            {'ticker': 'SNAP', 'name': 'Snap Inc.', 'exchange': 'NYSE', 'country': 'US', 'currency': 'USD'},
            {'ticker': 'ROKU', 'name': 'Roku, Inc.', 'exchange': 'NASDAQ', 'country': 'US', 'currency': 'USD'},
            {'ticker': 'SHOP', 'name': 'Shopify Inc.', 'exchange': 'NYSE', 'country': 'US', 'currency': 'USD'},
            {'ticker': 'COIN', 'name': 'Coinbase Global, Inc.', 'exchange': 'NASDAQ', 'country': 'US', 'currency': 'USD'},
            # Traditional stocks
            {'ticker': 'JPM', 'name': 'JPMorgan Chase & Co.', 'exchange': 'NYSE', 'country': 'US', 'currency': 'USD'},
            {'ticker': 'BAC', 'name': 'Bank of America Corporation', 'exchange': 'NYSE', 'country': 'US', 'currency': 'USD'},
            {'ticker': 'WFC', 'name': 'Wells Fargo & Company', 'exchange': 'NYSE', 'country': 'US', 'currency': 'USD'},
            {'ticker': 'GS', 'name': 'The Goldman Sachs Group, Inc.', 'exchange': 'NYSE', 'country': 'US', 'currency': 'USD'},
            {'ticker': 'JNJ', 'name': 'Johnson & Johnson', 'exchange': 'NYSE', 'country': 'US', 'currency': 'USD'},
            {'ticker': 'PFE', 'name': 'Pfizer Inc.', 'exchange': 'NYSE', 'country': 'US', 'currency': 'USD'},
            {'ticker': 'KO', 'name': 'The Coca-Cola Company', 'exchange': 'NYSE', 'country': 'US', 'currency': 'USD'},
            {'ticker': 'PEP', 'name': 'PepsiCo, Inc.', 'exchange': 'NASDAQ', 'country': 'US', 'currency': 'USD'},
            {'ticker': 'WMT', 'name': 'Walmart Inc.', 'exchange': 'NYSE', 'country': 'US', 'currency': 'USD'},
            {'ticker': 'HD', 'name': 'The Home Depot, Inc.', 'exchange': 'NYSE', 'country': 'US', 'currency': 'USD'},
            {'ticker': 'MCD', 'name': 'McDonald\'s Corporation', 'exchange': 'NYSE', 'country': 'US', 'currency': 'USD'},
            {'ticker': 'NKE', 'name': 'NIKE, Inc.', 'exchange': 'NYSE', 'country': 'US', 'currency': 'USD'},
            {'ticker': 'V', 'name': 'Visa Inc.', 'exchange': 'NYSE', 'country': 'US', 'currency': 'USD'},
            {'ticker': 'MA', 'name': 'Mastercard Incorporated', 'exchange': 'NYSE', 'country': 'US', 'currency': 'USD'},
            # ETFs
            {'ticker': 'SPY', 'name': 'SPDR S&P 500 ETF Trust', 'exchange': 'NYSE', 'country': 'US', 'currency': 'USD'},
            {'ticker': 'QQQ', 'name': 'Invesco QQQ Trust', 'exchange': 'NASDAQ', 'country': 'US', 'currency': 'USD'},
            {'ticker': 'VTI', 'name': 'Vanguard Total Stock Market ETF', 'exchange': 'NYSE', 'country': 'US', 'currency': 'USD'},
        ]

        query_lower = query.lower().strip()
        results = []

        # Search through popular stocks
        for stock in popular_stocks:
            # Match by ticker or company name
            if (query_lower in stock['ticker'].lower() or
                query_lower in stock['name'].lower()):

                results.append({
                    'ticker': stock['ticker'],
                    'name': stock['name'],
                    'exchange': stock['exchange'],
                    'type': 'Common Stock',
                    'country': stock['country'],
                    'currency': stock['currency'],
                    'display_name': f"{stock['name']} ({stock['ticker']})"
                })

                if len(results) >= limit:
                    break

        logger.info(f"Fallback search for '{query}' returned {len(results)} results")
        return results
    
    def get_available_metrics(self, ticker: str) -> List[Dict[str, Any]]:
        """Get available metrics for a ticker"""
        try:
            # Define metric categories with their display information
            metrics = [
                # Price Metrics
                {
                    'category': 'price',
                    'name': 'current_price',
                    'display_name': 'Current Price',
                    'description': 'Real-time stock price',
                    'visualization': 'simple',
                    'format': 'currency'
                },
                {
                    'category': 'price',
                    'name': 'day_change',
                    'display_name': 'Daily Change',
                    'description': 'Price change from previous close',
                    'visualization': 'simple',
                    'format': 'percentage'
                },
                {
                    'category': 'price',
                    'name': 'volume',
                    'display_name': 'Volume',
                    'description': 'Trading volume',
                    'visualization': 'simple',
                    'format': 'number'
                },
                
                # Valuation Metrics
                {
                    'category': 'valuation',
                    'name': 'pe_ratio',
                    'display_name': 'P/E Ratio',
                    'description': 'Price to Earnings ratio',
                    'visualization': 'simple',
                    'format': 'ratio'
                },
                {
                    'category': 'valuation',
                    'name': 'market_cap',
                    'display_name': 'Market Cap',
                    'description': 'Total market capitalization',
                    'visualization': 'simple',
                    'format': 'currency'
                },
                {
                    'category': 'valuation',
                    'name': 'book_value',
                    'display_name': 'Book Value',
                    'description': 'Book value per share',
                    'visualization': 'simple',
                    'format': 'currency'
                },
                {
                    'category': 'valuation',
                    'name': 'price_to_book',
                    'display_name': 'Price to Book',
                    'description': 'Price to book value ratio',
                    'visualization': 'simple',
                    'format': 'ratio'
                },
                
                # Technical Indicators
                {
                    'category': 'technical',
                    'name': 'rsi',
                    'display_name': 'RSI (14)',
                    'description': 'Relative Strength Index',
                    'visualization': 'gauge',
                    'format': 'percentage'
                },
                {
                    'category': 'technical',
                    'name': 'sma_50',
                    'display_name': 'SMA 50',
                    'description': '50-day Simple Moving Average',
                    'visualization': 'line_chart',
                    'format': 'currency'
                },
                {
                    'category': 'technical',
                    'name': 'sma_200',
                    'display_name': 'SMA 200',
                    'description': '200-day Simple Moving Average',
                    'visualization': 'line_chart',
                    'format': 'currency'
                },
                {
                    'category': 'technical',
                    'name': 'ema_12',
                    'display_name': 'EMA 12',
                    'description': '12-day Exponential Moving Average',
                    'visualization': 'line_chart',
                    'format': 'currency'
                },
                
                # Fundamental Metrics
                {
                    'category': 'fundamentals',
                    'name': 'total_revenue',
                    'display_name': 'Total Revenue',
                    'description': 'Quarterly total revenue with forecast capability',
                    'visualization': 'forecast_chart',
                    'format': 'currency'
                },
                {
                    'category': 'fundamentals',
                    'name': 'net_income',
                    'display_name': 'Net Income',
                    'description': 'Quarterly net income with forecast capability',
                    'visualization': 'forecast_chart',
                    'format': 'currency'
                },
                {
                    'category': 'fundamentals',
                    'name': 'free_cash_flow',
                    'display_name': 'Free Cash Flow',
                    'description': 'Quarterly free cash flow with forecast capability',
                    'visualization': 'forecast_chart',
                    'format': 'currency'
                },
                {
                    'category': 'fundamentals',
                    'name': 'total_assets',
                    'display_name': 'Total Assets',
                    'description': 'Total company assets',
                    'visualization': 'forecast_chart',
                    'format': 'currency'
                },
                {
                    'category': 'fundamentals',
                    'name': 'total_debt',
                    'display_name': 'Total Debt',
                    'description': 'Total company debt',
                    'visualization': 'forecast_chart',
                    'format': 'currency'
                },
                {
                    'category': 'fundamentals',
                    'name': 'operating_cash_flow',
                    'display_name': 'Operating Cash Flow',
                    'description': 'Cash flow from operations',
                    'visualization': 'forecast_chart',
                    'format': 'currency'
                }
            ]
            
            return metrics
            
        except Exception as e:
            logger.error(f"Error getting available metrics: {e}")
            return []
    
    def get_current_data(self, ticker: str, metric: str) -> Optional[Dict[str, Any]]:
        """Get current/real-time data for a specific metric"""
        try:
            # Check cache first (5 minute cache for real-time data)
            cache_key = f"current_{ticker}_{metric}"
            cached_data = self.cache.get('current', cache_key)
            
            if cached_data:
                return json.loads(cached_data)
            
            # Get real-time data
            real_time_data = self._make_request(f'real-time/{ticker}')
            
            # Get fundamental data if needed
            fundamental_data = None
            if metric in ['pe_ratio', 'market_cap', 'book_value', 'price_to_book']:
                fundamental_data = self._make_request(f'fundamentals/{ticker}')
            
            # Extract the specific metric value
            result = self._extract_metric_value(real_time_data, fundamental_data, metric)
            
            if result:
                # Cache for 5 minutes
                self.cache.set('current', cache_key, json.dumps(result),
                              datetime.utcnow() + timedelta(minutes=5))
            
            return result
            
        except Exception as e:
            logger.error(f"Error getting current data for {ticker}/{metric}: {e}")
            return None
    
    def get_historical_data(self, ticker: str, metric: str, period: str = '1y') -> List[Dict[str, Any]]:
        """Get historical data for a specific metric"""
        try:
            # Check cache first (30 minute cache for historical data)
            cache_key = f"historical_{ticker}_{metric}_{period}"
            cached_data = self.cache.get('historical', cache_key)
            
            if cached_data:
                return json.loads(cached_data)
            
            result = []
            
            if metric in ['current_price', 'volume', 'sma_50', 'sma_200', 'ema_12']:
                # Get price history
                end_date = datetime.now()
                start_date = end_date - timedelta(days=365 if period == '1y' else 90)
                
                price_data = self._make_request(f'eod/{ticker}', {
                    'from': start_date.strftime('%Y-%m-%d'),
                    'to': end_date.strftime('%Y-%m-%d')
                })
                
                if price_data:
                    result = self._process_price_history(price_data, metric)
            
            elif metric in ['total_revenue', 'net_income', 'free_cash_flow', 'operating_cash_flow']:
                # Get fundamental history
                fundamental_data = self._make_request(f'fundamentals/{ticker}')
                
                if fundamental_data:
                    result = self._process_fundamental_history(fundamental_data, metric)
            
            elif metric == 'rsi':
                # Calculate RSI from price data
                end_date = datetime.now()
                start_date = end_date - timedelta(days=365)
                
                price_data = self._make_request(f'eod/{ticker}', {
                    'from': start_date.strftime('%Y-%m-%d'),
                    'to': end_date.strftime('%Y-%m-%d')
                })
                
                if price_data:
                    result = self._calculate_rsi(price_data)
            
            if result:
                # Cache for 30 minutes
                self.cache.set('historical', cache_key, json.dumps(result),
                              datetime.utcnow() + timedelta(minutes=30))
            
            return result
            
        except Exception as e:
            logger.error(f"Error getting historical data for {ticker}/{metric}: {e}")
            return []
    
    def _extract_metric_value(self, real_time_data: Dict, fundamental_data: Dict, metric: str) -> Optional[Dict]:
        """Extract specific metric value from API responses"""
        try:
            if not real_time_data:
                return None
            
            result = {
                'timestamp': datetime.utcnow().isoformat(),
                'ticker': real_time_data.get('code', ''),
                'metric': metric
            }
            
            if metric == 'current_price':
                result['value'] = real_time_data.get('close', 0)
                result['previous_close'] = real_time_data.get('previousClose', 0)
                
            elif metric == 'day_change':
                current = real_time_data.get('close', 0)
                previous = real_time_data.get('previousClose', 0)
                change = ((current - previous) / previous * 100) if previous else 0
                result['value'] = change
                result['absolute_change'] = current - previous
                
            elif metric == 'volume':
                result['value'] = real_time_data.get('volume', 0)
                
            elif metric in ['pe_ratio', 'market_cap', 'book_value', 'price_to_book']:
                if fundamental_data and 'Highlights' in fundamental_data:
                    highlights = fundamental_data['Highlights']
                    mapping = {
                        'pe_ratio': 'PERatio',
                        'market_cap': 'MarketCapitalization',
                        'book_value': 'BookValue',
                        'price_to_book': 'PriceToBookMRQ'
                    }
                    result['value'] = highlights.get(mapping.get(metric), 0)
            
            return result
            
        except Exception as e:
            logger.error(f"Error extracting metric value: {e}")
            return None
    
    def _process_price_history(self, price_data: List[Dict], metric: str) -> List[Dict]:
        """Process price history data for specific metric"""
        try:
            result = []
            
            for item in price_data:
                if isinstance(item, dict):
                    data_point = {
                        'date': item.get('date'),
                        'metric': metric
                    }
                    
                    if metric == 'current_price':
                        data_point['value'] = item.get('close', 0)
                    elif metric == 'volume':
                        data_point['value'] = item.get('volume', 0)
                    elif metric in ['sma_50', 'sma_200', 'ema_12']:
                        # For moving averages, use close price (in real implementation, 
                        # you'd calculate the actual moving averages)
                        data_point['value'] = item.get('close', 0)
                    
                    result.append(data_point)
            
            return result
            
        except Exception as e:
            logger.error(f"Error processing price history: {e}")
            return []
    
    def _process_fundamental_history(self, fundamental_data: Dict, metric: str) -> List[Dict]:
        """Process fundamental history data"""
        try:
            result = []
            
            if 'Financials' in fundamental_data:
                financials = fundamental_data['Financials']
                
                # Map metric names to EODHD field names
                field_mapping = {
                    'total_revenue': 'totalRevenue',
                    'net_income': 'netIncome',
                    'free_cash_flow': 'freeCashFlow',
                    'operating_cash_flow': 'totalCashFromOperatingActivities',
                    'total_assets': 'totalAssets',
                    'total_debt': 'totalDebt'
                }
                
                field_name = field_mapping.get(metric)
                if not field_name:
                    return result
                
                # Process quarterly data
                if 'Income_Statement' in financials and 'quarterly' in financials['Income_Statement']:
                    quarterly_data = financials['Income_Statement']['quarterly']
                    
                    for date, data in quarterly_data.items():
                        if isinstance(data, dict) and field_name in data:
                            result.append({
                                'date': date,
                                'value': data[field_name],
                                'metric': metric,
                                'period': 'quarterly'
                            })
                
                # Process yearly data
                if 'Income_Statement' in financials and 'yearly' in financials['Income_Statement']:
                    yearly_data = financials['Income_Statement']['yearly']
                    
                    for date, data in yearly_data.items():
                        if isinstance(data, dict) and field_name in data:
                            result.append({
                                'date': date,
                                'value': data[field_name],
                                'metric': metric,
                                'period': 'yearly'
                            })
            
            # Sort by date
            result.sort(key=lambda x: x['date'])
            return result
            
        except Exception as e:
            logger.error(f"Error processing fundamental history: {e}")
            return []
    
    def _calculate_rsi(self, price_data: List[Dict], period: int = 14) -> List[Dict]:
        """Calculate RSI from price data"""
        try:
            if len(price_data) < period + 1:
                return []
            
            result = []
            closes = [float(item.get('close', 0)) for item in price_data]
            
            # Calculate price changes
            deltas = [closes[i] - closes[i-1] for i in range(1, len(closes))]
            
            # Separate gains and losses
            gains = [delta if delta > 0 else 0 for delta in deltas]
            losses = [-delta if delta < 0 else 0 for delta in deltas]
            
            # Calculate initial averages
            avg_gain = sum(gains[:period]) / period
            avg_loss = sum(losses[:period]) / period
            
            # Calculate RSI for each point
            for i in range(period, len(price_data)):
                if avg_loss != 0:
                    rs = avg_gain / avg_loss
                    rsi = 100 - (100 / (1 + rs))
                else:
                    rsi = 100
                
                result.append({
                    'date': price_data[i].get('date'),
                    'value': rsi,
                    'metric': 'rsi'
                })
                
                # Update averages for next iteration
                if i < len(deltas):
                    avg_gain = (avg_gain * (period - 1) + gains[i]) / period
                    avg_loss = (avg_loss * (period - 1) + losses[i]) / period
            
            return result
            
        except Exception as e:
            logger.error(f"Error calculating RSI: {e}")
            return []