"""
Notification Service for Stock Watchlist Dashboard

This service handles all notification operations including:
- Creating and managing notifications
- Background checking for alert conditions
- Email notifications (if configured)
- Real-time notification delivery
"""

import logging
import smtplib
from datetime import datetime, timedelta
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMult<PERSON>art
from typing import Dict, List, Optional, Any
from models.database import NotificationModel

logger = logging.getLogger(__name__)

class NotificationService:
    """Service for managing notifications and alerts"""
    
    def __init__(self):
        self.notification_model = NotificationModel()
        
        # Email configuration (optional)
        self.smtp_server = None
        self.smtp_port = None
        self.smtp_username = None
        self.smtp_password = None
        self.from_email = None
    
    def configure_email(self, smtp_server: str, smtp_port: int, 
                       username: str, password: str, from_email: str):
        """Configure email settings for notifications"""
        self.smtp_server = smtp_server
        self.smtp_port = smtp_port
        self.smtp_username = username
        self.smtp_password = password
        self.from_email = from_email
    
    def create_alert_notification(self, user_id: str, ticker: str, metric_name: str,
                                 condition_type: str, target_value: float, 
                                 current_value: float, description: str = '') -> bool:
        """Create a notification for an alert condition being met"""
        try:
            # Format the alert message
            message = self._format_alert_message(
                ticker, metric_name, condition_type, target_value, current_value, description
            )
            
            # Create notification in database
            notification_id = self.notification_model.create(
                user_id=user_id,
                ticker=ticker,
                metric_name=metric_name,
                message=message,
                notification_type='alert'
            )
            
            if notification_id:
                logger.info(f"Alert notification created for {user_id}: {ticker}/{metric_name}")
                return True
            
            return False
            
        except Exception as e:
            logger.error(f"Error creating alert notification: {e}")
            return False
    
    def create_forecast_notification(self, user_id: str, ticker: str, metric_name: str,
                                   forecast_value: float, actual_value: float,
                                   forecast_date: str, description: str = '') -> bool:
        """Create a notification for a forecast being achieved"""
        try:
            # Format the forecast message
            message = self._format_forecast_message(
                ticker, metric_name, forecast_value, actual_value, forecast_date, description
            )
            
            # Create notification in database
            notification_id = self.notification_model.create(
                user_id=user_id,
                ticker=ticker,
                metric_name=metric_name,
                message=message,
                notification_type='forecast'
            )
            
            if notification_id:
                logger.info(f"Forecast notification created for {user_id}: {ticker}/{metric_name}")
                return True
            
            return False
            
        except Exception as e:
            logger.error(f"Error creating forecast notification: {e}")
            return False
    
    def create_system_notification(self, user_id: str, message: str, 
                                  notification_type: str = 'system') -> bool:
        """Create a system notification"""
        try:
            notification_id = self.notification_model.create(
                user_id=user_id,
                ticker='SYSTEM',
                metric_name='system',
                message=message,
                notification_type=notification_type
            )
            
            return notification_id is not None
            
        except Exception as e:
            logger.error(f"Error creating system notification: {e}")
            return False
    
    def get_user_notifications(self, user_id: str, limit: int = 50) -> List[Dict[str, Any]]:
        """Get notifications for a user"""
        try:
            notifications = self.notification_model.get_by_user(user_id, limit)
            
            # Format notifications for frontend
            formatted_notifications = []
            for notification in notifications:
                formatted_notification = {
                    'id': notification['id'],
                    'ticker': notification['ticker'],
                    'metric_name': notification['metric_name'],
                    'message': notification['message'],
                    'type': notification['notification_type'],
                    'is_read': bool(notification['is_read']),
                    'created_at': notification['created_at'],
                    'time_ago': self._time_ago(notification['created_at']),
                    'priority': self._get_notification_priority(notification['notification_type'])
                }
                formatted_notifications.append(formatted_notification)
            
            return formatted_notifications
            
        except Exception as e:
            logger.error(f"Error getting user notifications: {e}")
            return []
    
    def mark_as_read(self, user_id: str, notification_id: int) -> bool:
        """Mark a notification as read"""
        try:
            return self.notification_model.mark_as_read(user_id, notification_id)
        except Exception as e:
            logger.error(f"Error marking notification as read: {e}")
            return False
    
    def mark_all_as_read(self, user_id: str) -> bool:
        """Mark all notifications as read for a user"""
        try:
            # Get all unread notifications
            notifications = self.get_user_notifications(user_id)
            unread_notifications = [n for n in notifications if not n['is_read']]
            
            # Mark each as read
            for notification in unread_notifications:
                self.mark_as_read(user_id, notification['id'])
            
            return True
            
        except Exception as e:
            logger.error(f"Error marking all notifications as read: {e}")
            return False
    
    def get_unread_count(self, user_id: str) -> int:
        """Get count of unread notifications"""
        try:
            return self.notification_model.get_unread_count(user_id)
        except Exception as e:
            logger.error(f"Error getting unread count: {e}")
            return 0
    
    def send_email_notification(self, user_email: str, subject: str, 
                               message: str, html_message: str = None) -> bool:
        """Send email notification (if email is configured)"""
        try:
            if not all([self.smtp_server, self.smtp_port, self.smtp_username, 
                       self.smtp_password, self.from_email]):
                logger.warning("Email not configured, skipping email notification")
                return False
            
            # Create message
            msg = MIMEMultipart('alternative')
            msg['From'] = self.from_email
            msg['To'] = user_email
            msg['Subject'] = subject
            
            # Add text version
            text_part = MIMEText(message, 'plain')
            msg.attach(text_part)
            
            # Add HTML version if provided
            if html_message:
                html_part = MIMEText(html_message, 'html')
                msg.attach(html_part)
            
            # Send email
            with smtplib.SMTP(self.smtp_server, self.smtp_port) as server:
                server.starttls()
                server.login(self.smtp_username, self.smtp_password)
                server.send_message(msg)
            
            logger.info(f"Email notification sent to {user_email}")
            return True
            
        except Exception as e:
            logger.error(f"Error sending email notification: {e}")
            return False
    
    def cleanup_old_notifications(self, days_old: int = 30) -> int:
        """Clean up old notifications"""
        try:
            cutoff_date = datetime.utcnow() - timedelta(days=days_old)
            # This would require a database method to delete old notifications
            # For now, just return 0
            logger.info(f"Would clean up notifications older than {cutoff_date}")
            return 0
            
        except Exception as e:
            logger.error(f"Error cleaning up old notifications: {e}")
            return 0
    
    def _format_alert_message(self, ticker: str, metric_name: str, condition_type: str,
                             target_value: float, current_value: float, description: str) -> str:
        """Format an alert message"""
        try:
            # Convert condition type to readable format
            condition_text = {
                'greater_than': 'exceeded',
                'less_than': 'dropped below',
                'equals': 'reached',
                'crosses_above': 'crossed above',
                'crosses_below': 'crossed below'
            }.get(condition_type, 'met condition for')
            
            # Format metric name
            metric_display = metric_name.replace('_', ' ').title()
            
            # Format values based on metric type
            if 'price' in metric_name or 'revenue' in metric_name or 'cash_flow' in metric_name:
                target_str = f"${target_value:,.2f}"
                current_str = f"${current_value:,.2f}"
            elif 'ratio' in metric_name or metric_name == 'pe_ratio':
                target_str = f"{target_value:.2f}"
                current_str = f"{current_value:.2f}"
            elif 'percentage' in metric_name or metric_name == 'rsi':
                target_str = f"{target_value:.1f}%"
                current_str = f"{current_value:.1f}%"
            else:
                target_str = f"{target_value:,.2f}"
                current_str = f"{current_value:,.2f}"
            
            message = f"🚨 Alert: {ticker} {metric_display} has {condition_text} {target_str} (Current: {current_str})"
            
            if description:
                message += f"\nNote: {description}"
            
            return message
            
        except Exception as e:
            logger.error(f"Error formatting alert message: {e}")
            return f"Alert for {ticker} {metric_name}"
    
    def _format_forecast_message(self, ticker: str, metric_name: str, forecast_value: float,
                                actual_value: float, forecast_date: str, description: str) -> str:
        """Format a forecast achievement message"""
        try:
            # Format metric name
            metric_display = metric_name.replace('_', ' ').title()
            
            # Format values
            if 'price' in metric_name or 'revenue' in metric_name or 'cash_flow' in metric_name:
                forecast_str = f"${forecast_value:,.2f}"
                actual_str = f"${actual_value:,.2f}"
            else:
                forecast_str = f"{forecast_value:,.2f}"
                actual_str = f"{actual_value:,.2f}"
            
            # Calculate accuracy
            accuracy = (1 - abs(actual_value - forecast_value) / forecast_value) * 100
            
            message = f"🎯 Forecast Achieved: {ticker} {metric_display} reached {actual_str} "
            message += f"(You predicted: {forecast_str}, {accuracy:.1f}% accurate!)"
            
            if description:
                message += f"\nYour note: {description}"
            
            return message
            
        except Exception as e:
            logger.error(f"Error formatting forecast message: {e}")
            return f"Forecast achieved for {ticker} {metric_name}"
    
    def _time_ago(self, timestamp: str) -> str:
        """Convert timestamp to time ago string"""
        try:
            created_time = datetime.fromisoformat(timestamp.replace('Z', '+00:00'))
            now = datetime.utcnow()
            delta = now - created_time
            
            if delta.days > 0:
                return f"{delta.days} day{'s' if delta.days != 1 else ''} ago"
            elif delta.seconds > 3600:
                hours = delta.seconds // 3600
                return f"{hours} hour{'s' if hours != 1 else ''} ago"
            elif delta.seconds > 60:
                minutes = delta.seconds // 60
                return f"{minutes} minute{'s' if minutes != 1 else ''} ago"
            else:
                return "Just now"
                
        except Exception as e:
            logger.error(f"Error calculating time ago: {e}")
            return "Unknown"
    
    def _get_notification_priority(self, notification_type: str) -> str:
        """Get priority level for notification type"""
        priority_map = {
            'alert': 'high',
            'forecast': 'medium',
            'system': 'low'
        }
        return priority_map.get(notification_type, 'low')