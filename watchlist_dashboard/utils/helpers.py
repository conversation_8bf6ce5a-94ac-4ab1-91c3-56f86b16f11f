"""
Helper utilities for Stock Watchlist Dashboard

This module contains utility functions for formatting, validation,
and other common operations used throughout the application.
"""

import re
import logging
from datetime import datetime, timedelta
from typing import Union, Optional, Dict, Any

logger = logging.getLogger(__name__)

def format_currency(value: Union[float, int, None], currency: str = 'USD') -> str:
    """Format a number as currency"""
    try:
        if value is None or value == 0:
            return '$0.00'
        
        # Handle large numbers with abbreviations
        abs_value = abs(value)
        
        if abs_value >= 1_000_000_000_000:  # Trillions
            formatted = f"${value / 1_000_000_000_000:.2f}T"
        elif abs_value >= 1_000_000_000:  # Billions
            formatted = f"${value / 1_000_000_000:.2f}B"
        elif abs_value >= 1_000_000:  # Millions
            formatted = f"${value / 1_000_000:.2f}M"
        elif abs_value >= 1_000:  # Thousands
            formatted = f"${value / 1_000:.2f}K"
        else:
            formatted = f"${value:.2f}"
        
        return formatted
        
    except (ValueError, TypeError) as e:
        logger.error(f"Error formatting currency: {e}")
        return '$0.00'

def format_percentage(value: Union[float, int, None], decimals: int = 2) -> str:
    """Format a number as percentage"""
    try:
        if value is None:
            return '0.00%'
        
        return f"{value:.{decimals}f}%"
        
    except (ValueError, TypeError) as e:
        logger.error(f"Error formatting percentage: {e}")
        return '0.00%'

def format_number(value: Union[float, int, None], decimals: int = 2) -> str:
    """Format a number with appropriate abbreviations"""
    try:
        if value is None or value == 0:
            return '0'
        
        abs_value = abs(value)
        
        if abs_value >= 1_000_000_000_000:  # Trillions
            formatted = f"{value / 1_000_000_000_000:.{decimals}f}T"
        elif abs_value >= 1_000_000_000:  # Billions
            formatted = f"{value / 1_000_000_000:.{decimals}f}B"
        elif abs_value >= 1_000_000:  # Millions
            formatted = f"{value / 1_000_000:.{decimals}f}M"
        elif abs_value >= 1_000:  # Thousands
            formatted = f"{value / 1_000:.{decimals}f}K"
        else:
            formatted = f"{value:.{decimals}f}"
        
        return formatted
        
    except (ValueError, TypeError) as e:
        logger.error(f"Error formatting number: {e}")
        return '0'

def format_ratio(value: Union[float, int, None], decimals: int = 2) -> str:
    """Format a ratio value"""
    try:
        if value is None:
            return 'N/A'
        
        if value == float('inf') or value == float('-inf'):
            return '∞'
        
        return f"{value:.{decimals}f}"
        
    except (ValueError, TypeError) as e:
        logger.error(f"Error formatting ratio: {e}")
        return 'N/A'

def format_date(date_str: str, format_type: str = 'short') -> str:
    """Format date string"""
    try:
        if not date_str:
            return ''
        
        # Parse the date
        if 'T' in date_str:
            date_obj = datetime.fromisoformat(date_str.replace('Z', '+00:00'))
        else:
            date_obj = datetime.strptime(date_str, '%Y-%m-%d')
        
        # Format based on type
        if format_type == 'short':
            return date_obj.strftime('%m/%d/%Y')
        elif format_type == 'medium':
            return date_obj.strftime('%b %d, %Y')
        elif format_type == 'long':
            return date_obj.strftime('%B %d, %Y')
        elif format_type == 'datetime':
            return date_obj.strftime('%m/%d/%Y %I:%M %p')
        else:
            return date_obj.strftime('%Y-%m-%d')
            
    except (ValueError, TypeError) as e:
        logger.error(f"Error formatting date: {e}")
        return date_str or ''

def validate_ticker(ticker: str) -> bool:
    """Validate ticker symbol format"""
    try:
        if not ticker:
            return False
        
        # Basic ticker validation (1-5 letters, optional exchange suffix)
        pattern = r'^[A-Z]{1,5}(\.[A-Z]{1,3})?$'
        return bool(re.match(pattern, ticker.upper()))
        
    except Exception as e:
        logger.error(f"Error validating ticker: {e}")
        return False

def validate_email(email: str) -> bool:
    """Validate email address format"""
    try:
        if not email:
            return False
        
        pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        return bool(re.match(pattern, email))
        
    except Exception as e:
        logger.error(f"Error validating email: {e}")
        return False

def calculate_change_percentage(current: float, previous: float) -> float:
    """Calculate percentage change between two values"""
    try:
        if previous == 0:
            return 0.0
        
        return ((current - previous) / previous) * 100
        
    except (ValueError, TypeError, ZeroDivisionError) as e:
        logger.error(f"Error calculating change percentage: {e}")
        return 0.0

def get_metric_display_name(metric_name: str) -> str:
    """Get display name for metric"""
    display_names = {
        'current_price': 'Current Price',
        'day_change': 'Daily Change',
        'volume': 'Volume',
        'pe_ratio': 'P/E Ratio',
        'market_cap': 'Market Cap',
        'book_value': 'Book Value',
        'price_to_book': 'Price to Book',
        'rsi': 'RSI (14)',
        'sma_50': 'SMA 50',
        'sma_200': 'SMA 200',
        'ema_12': 'EMA 12',
        'total_revenue': 'Total Revenue',
        'net_income': 'Net Income',
        'free_cash_flow': 'Free Cash Flow',
        'operating_cash_flow': 'Operating Cash Flow',
        'total_assets': 'Total Assets',
        'total_debt': 'Total Debt'
    }
    
    return display_names.get(metric_name, metric_name.replace('_', ' ').title())

def get_metric_format(metric_name: str) -> str:
    """Get format type for metric"""
    currency_metrics = [
        'current_price', 'book_value', 'total_revenue', 'net_income',
        'free_cash_flow', 'operating_cash_flow', 'total_assets', 'total_debt',
        'market_cap', 'sma_50', 'sma_200', 'ema_12'
    ]
    
    percentage_metrics = ['day_change', 'rsi']
    
    ratio_metrics = ['pe_ratio', 'price_to_book']
    
    if metric_name in currency_metrics:
        return 'currency'
    elif metric_name in percentage_metrics:
        return 'percentage'
    elif metric_name in ratio_metrics:
        return 'ratio'
    else:
        return 'number'

def format_metric_value(value: Union[float, int, None], metric_name: str) -> str:
    """Format a metric value based on its type"""
    try:
        format_type = get_metric_format(metric_name)
        
        if format_type == 'currency':
            return format_currency(value)
        elif format_type == 'percentage':
            return format_percentage(value)
        elif format_type == 'ratio':
            return format_ratio(value)
        else:
            return format_number(value)
            
    except Exception as e:
        logger.error(f"Error formatting metric value: {e}")
        return 'N/A'

def get_color_for_change(value: float) -> str:
    """Get color class for change values"""
    try:
        if value > 0:
            return 'positive'
        elif value < 0:
            return 'negative'
        else:
            return 'neutral'
            
    except (ValueError, TypeError):
        return 'neutral'

def truncate_text(text: str, max_length: int = 100) -> str:
    """Truncate text to specified length"""
    try:
        if not text:
            return ''
        
        if len(text) <= max_length:
            return text
        
        return text[:max_length - 3] + '...'
        
    except Exception as e:
        logger.error(f"Error truncating text: {e}")
        return str(text) if text else ''

def safe_divide(numerator: float, denominator: float, default: float = 0.0) -> float:
    """Safely divide two numbers"""
    try:
        if denominator == 0:
            return default
        
        return numerator / denominator
        
    except (ValueError, TypeError):
        return default

def parse_date_input(date_input: str) -> Optional[datetime]:
    """Parse various date input formats"""
    try:
        if not date_input:
            return None
        
        # Try different date formats
        formats = [
            '%Y-%m-%d',
            '%m/%d/%Y',
            '%m-%d-%Y',
            '%Y-%m-%dT%H:%M:%S',
            '%Y-%m-%dT%H:%M:%S.%f',
            '%Y-%m-%dT%H:%M:%SZ'
        ]
        
        for fmt in formats:
            try:
                return datetime.strptime(date_input, fmt)
            except ValueError:
                continue
        
        # If no format matches, return None
        return None
        
    except Exception as e:
        logger.error(f"Error parsing date input: {e}")
        return None

def get_time_range_dates(range_type: str) -> tuple[datetime, datetime]:
    """Get start and end dates for time range"""
    try:
        end_date = datetime.utcnow()
        
        if range_type == '1d':
            start_date = end_date - timedelta(days=1)
        elif range_type == '1w':
            start_date = end_date - timedelta(weeks=1)
        elif range_type == '1m':
            start_date = end_date - timedelta(days=30)
        elif range_type == '3m':
            start_date = end_date - timedelta(days=90)
        elif range_type == '6m':
            start_date = end_date - timedelta(days=180)
        elif range_type == '1y':
            start_date = end_date - timedelta(days=365)
        elif range_type == '2y':
            start_date = end_date - timedelta(days=730)
        elif range_type == '5y':
            start_date = end_date - timedelta(days=1825)
        else:
            # Default to 1 year
            start_date = end_date - timedelta(days=365)
        
        return start_date, end_date
        
    except Exception as e:
        logger.error(f"Error getting time range dates: {e}")
        return datetime.utcnow() - timedelta(days=365), datetime.utcnow()

def sanitize_filename(filename: str) -> str:
    """Sanitize filename for safe storage"""
    try:
        if not filename:
            return 'untitled'
        
        # Remove or replace unsafe characters
        safe_filename = re.sub(r'[<>:"/\\|?*]', '_', filename)
        safe_filename = re.sub(r'\s+', '_', safe_filename)
        safe_filename = safe_filename.strip('._')
        
        return safe_filename or 'untitled'
        
    except Exception as e:
        logger.error(f"Error sanitizing filename: {e}")
        return 'untitled'

def generate_cache_key(*args) -> str:
    """Generate a cache key from arguments"""
    try:
        key_parts = [str(arg) for arg in args if arg is not None]
        return '_'.join(key_parts).lower()
        
    except Exception as e:
        logger.error(f"Error generating cache key: {e}")
        return 'default_cache_key'