#!/usr/bin/env python3
"""
Start Both Applications Script

This script starts both the original H Trader Pro application 
and the new Stock Watchlist Dashboard simultaneously.
"""

import subprocess
import sys
import time
import os
from pathlib import Path

def start_original_app():
    """Start the original H Trader Pro application on port 5000"""
    print("🚀 Starting H Trader Pro (Original App) on http://localhost:5000")
    return subprocess.Popen([
        sys.executable, "app.py"
    ], cwd="/Users/<USER>/Downloads/cd-2")

def start_watchlist_app():
    """Start the new Stock Watchlist Dashboard on port 5001"""
    print("🚀 Starting Stock Watchlist Dashboard on http://localhost:5001")
    return subprocess.Popen([
        sys.executable, "app.py"
    ], cwd="/Users/<USER>/Downloads/cd-2/watchlist_dashboard")

def main():
    print("=" * 60)
    print("🚀 STARTING BOTH APPLICATIONS")
    print("=" * 60)
    
    # Start both applications
    try:
        original_proc = start_original_app()
        time.sleep(2)  # Give first app time to start
        
        watchlist_proc = start_watchlist_app()
        time.sleep(2)  # Give second app time to start
        
        print("\n" + "=" * 60)
        print("✅ BOTH APPLICATIONS STARTED SUCCESSFULLY!")
        print("=" * 60)
        print("📱 H Trader Pro (Original):     http://localhost:5000")
        print("📊 Stock Watchlist Dashboard:  http://localhost:5001")
        print("=" * 60)
        print("\n💡 Tips:")
        print("• Visit the original app at http://localhost:5000")
        print("• Click the new 'Stock Watchlist Dashboard' icon in the sidebar")
        print("• Or directly visit http://localhost:5001")
        print("• Press Ctrl+C to stop both applications")
        print("\n🎯 The sidebar now includes a link to the new dashboard!")
        
        # Wait for processes to complete or for user to interrupt
        try:
            original_proc.wait()
            watchlist_proc.wait()
        except KeyboardInterrupt:
            print("\n\n🛑 Stopping applications...")
            original_proc.terminate()
            watchlist_proc.terminate()
            print("✅ Both applications stopped successfully!")
            
    except Exception as e:
        print(f"❌ Error starting applications: {e}")
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())